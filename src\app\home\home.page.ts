import { Component } from '@angular/core';
import { IonHeader, IonToolbar, IonTitle, IonContent } from '@ionic/angular/standalone';
import { Router } from '@angular/router';
import { PatientEntryPage } from '../modules/patient-entry/patient-entry.page';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  imports: [IonHeader, IonToolbar, IonTitle, IonContent, PatientEntryPage],
})
export class HomePage {
  constructor(private router: Router) {}

  navigateToPatientEntry() {
    this.router.navigate(['/patient-entry']);
  }
}
