import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { takeUntil, switchMap, catchError } from 'rxjs/operators';
import {
    DeviceLaunchOptions,
    DeviceCommunicationResult,
    DeviceStatus,
    DeviceType,
    PlatformType,
    CommunicationMethod,
    DeviceCommunicationProvider,
    PulseOximeterData,
    StethoscopeData,
    ThermometerData,
    BloodPressureData,
    ECGData
} from '../interfaces/medical-device.interface';
import { MedicalDeviceConfig } from '../config/medical-device.config';
import { PlatformDetectorService } from './platform-detector.service';
import { AndroidIntentService } from './android-intent.service';
import { WebSocketDeviceService } from './websocket-device.service';

/**
 * Master Medical Device Communication Service
 * Enterprise-level service that provides unified interface for cross-platform medical device communication
 */
@Injectable({
    providedIn: 'root'
})
export class MedicalDeviceCommunicationService implements On<PERSON><PERSON>roy {

    private destroy$ = new Subject<void>();
    private currentProvider: DeviceCommunicationProvider | null = null;
    private activeConnections = new Map<DeviceType, string>();

    // Public observables
    public deviceData$ = new BehaviorSubject<DeviceCommunicationResult | null>(null);
    public connectionStatus$ = new BehaviorSubject<Map<DeviceType, DeviceConnectionStatus>>(new Map());
    public platformInfo$ = new BehaviorSubject<PlatformInfo | null>(null);

    constructor(
        private platformDetector: PlatformDetectorService,
        private androidIntentService: AndroidIntentService,
        private webSocketDeviceService: WebSocketDeviceService
    ) {
        this.initializeService();
    }

    /**
     * Initialize the service and set up platform-specific provider
     */
    private async initializeService(): Promise<void> {
        try {
            console.log('🚀 Initializing Medical Device Communication Service');

            // Set platform information
            const platformInfo: PlatformInfo = {
                platform: this.platformDetector.currentPlatform,
                capabilities: this.platformDetector.capabilities,
                config: this.platformDetector.getPlatformConfig()
            };
            this.platformInfo$.next(platformInfo);

            console.log('🔍 Platform Detection:', {
                platform: platformInfo.platform,
                capabilities: platformInfo.capabilities
            });

            // Initialize platform-specific provider
            this.currentProvider = this.getPlatformProvider();

            // Set up data stream from the current provider
            this.setupProviderDataStream();

            // Initialize platform-specific event listeners
            await this.initializePlatformListeners();

            console.log('✅ Medical Device Communication Service initialized', {
                platform: platformInfo.platform,
                provider: this.currentProvider?.constructor.name,
                supportedDevices: this.currentProvider?.supportedDevices
            });

        } catch (error) {
            console.error('❌ Failed to initialize Medical Device Communication Service:', this.stringifyError(error));
        }
    }

    /**
     * Launch a medical device
     */
    async launchDevice(options: DeviceLaunchOptions): Promise<DeviceCommunicationResult> {
        try {
            console.log('🚀 Launching medical device:', options);

            // Validate platform support
            if (!this.currentProvider) {
                throw new Error('No communication provider available for current platform');
            }

            // Validate device support
            if (!this.currentProvider.supportedDevices.includes(options.deviceType)) {
                throw new Error(`Device ${options.deviceType} not supported on ${this.currentProvider.platform}`);
            }

            // Validate configuration
            if (!MedicalDeviceConfig.validateDeviceConfig(this.currentProvider.platform, options.deviceType)) {
                throw new Error(`Invalid configuration for ${options.deviceType} on ${this.currentProvider.platform}`);
            }

            // Add default options
            const enhancedOptions = {
                ...MedicalDeviceConfig.getDefaultLaunchOptions(options.deviceType),
                ...options,
                sessionId: this.generateSessionId()
            };

            // Launch device using current provider
            const result = await this.currentProvider.launchDevice(enhancedOptions);

            // Track active connection
            if (result.success) {
                this.activeConnections.set(options.deviceType, enhancedOptions.sessionId);
                this.updateConnectionStatus(options.deviceType, 'connecting');
            }

            // Emit result
            this.deviceData$.next(result);

            return result;

        } catch (error) {
            console.error('❌ Device launch failed:', this.stringifyError(error));

            const errorResult: DeviceCommunicationResult = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                platform: this.currentProvider?.platform || PlatformType.WEB,
                deviceType: options.deviceType,
                timestamp: Date.now()
            };

            this.deviceData$.next(errorResult);
            return errorResult;
        }
    }

    /**
     * Get device status
     */
    async getDeviceStatus(deviceType: DeviceType): Promise<DeviceStatus> {
        if (!this.currentProvider) {
            throw new Error('No communication provider available');
        }

        return await this.currentProvider.getDeviceStatus(deviceType);
    }

    /**
     * Disconnect from device
     */
    async disconnectDevice(deviceType: DeviceType): Promise<void> {
        try {
            if (!this.currentProvider) {
                throw new Error('No communication provider available');
            }

            await this.currentProvider.disconnect(deviceType);

            // Remove from active connections
            this.activeConnections.delete(deviceType);
            this.updateConnectionStatus(deviceType, 'disconnected');

            console.log('🔌 Device disconnected:', deviceType);

        } catch (error) {
            console.error('❌ Device disconnect failed:', this.stringifyError(error));
            throw error;
        }
    }

    /**
     * Get device data observable
     */
    getDeviceDataObservable(): Observable<DeviceCommunicationResult> {
        return this.deviceData$.asObservable().pipe(
            takeUntil(this.destroy$),
            // Filter out null values
            switchMap(data => data ? [data] : []),
            catchError(error => {
                console.error('❌ Device data observable error:', this.stringifyError(error));
                throw error;
            })
        );
    }

    /**
     * Get connection status observable
     */
    getConnectionStatusObservable(): Observable<Map<DeviceType, DeviceConnectionStatus>> {
        return this.connectionStatus$.asObservable();
    }

    /**
     * Get platform information observable
     */
    getPlatformInfoObservable(): Observable<PlatformInfo> {
        return this.platformInfo$.asObservable().pipe(
            takeUntil(this.destroy$),
            // Filter out null values
            switchMap(info => info ? [info] : [])
        );
    }

    /**
     * Check if device type is supported on current platform
     */
    isDeviceSupported(deviceType: DeviceType): boolean {
        return this.currentProvider?.supportedDevices.includes(deviceType) || false;
    }

    /**
     * Get supported devices for current platform
     */
    getSupportedDevices(): DeviceType[] {
        return this.currentProvider?.supportedDevices || [];
    }

    /**
     * Get current platform type
     */
    getCurrentPlatform(): PlatformType {
        return this.platformDetector.currentPlatform;
    }

    /**
     * Get communication method for device type
     */
    getCommunicationMethod(deviceType: DeviceType): CommunicationMethod {
        return MedicalDeviceConfig.getCommunicationMethod(
            this.getCurrentPlatform(),
            deviceType
        );
    }

    /**
     * Get active connections
     */
    getActiveConnections(): Map<DeviceType, string> {
        return new Map(this.activeConnections);
    }

    /**
     * Check if device is currently connected
     */
    isDeviceConnected(deviceType: DeviceType): boolean {
        return this.activeConnections.has(deviceType);
    }

    /**
     * Get platform-specific provider
     */
    private getPlatformProvider(): DeviceCommunicationProvider {
        console.log('🔌 Selecting provider for platform:', this.platformDetector.currentPlatform);

        switch (this.platformDetector.currentPlatform) {
            case PlatformType.ANDROID:
                console.log('📱 Using Android Intent Service for Android platform');
                return this.androidIntentService;

            case PlatformType.WEB:
                console.log('🌐 Using WebSocket Device Service for Web platform');
                return this.webSocketDeviceService;

            case PlatformType.ELECTRON:
                console.log('💻 Using WebSocket Device Service for Electron platform');
                return this.webSocketDeviceService;

            default:
                console.warn('⚠️ Unknown platform, defaulting to WebSocket Device Service:', this.platformDetector.currentPlatform);
                return this.webSocketDeviceService;
        }
    }

    /**
     * Set up data stream from current provider
     */
    private setupProviderDataStream(): void {
        if (!this.currentProvider) return;

        // For Android Intent Service
        if (this.currentProvider instanceof AndroidIntentService) {
            // Android intent service doesn't have a continuous data stream
            // Data comes through callbacks which are handled in the service itself
            console.log('📡 Android Intent Service data stream ready');
        }

        // For WebSocket Device Service - only initialize if not on Android
        if (this.currentProvider instanceof WebSocketDeviceService && this.platformDetector.currentPlatform !== PlatformType.ANDROID) {
            this.currentProvider.getDeviceDataObservable(DeviceType.PULSE_OXIMETER)
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (data) => {
                        this.handleProviderData(data);
                    },
                    error: (error) => {
                        console.error('❌ Provider data stream error:', this.stringifyError(error));
                    }
                });

            // Subscribe to connection status updates
            this.currentProvider.getConnectionStatusObservable()
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (statusMap) => {
                        this.handleConnectionStatusUpdate(statusMap);
                    }
                });

            console.log('📡 WebSocket Device Service data stream ready');
        }
    }

    /**
     * Initialize platform-specific event listeners
     */
    private async initializePlatformListeners(): Promise<void> {
        try {
            if (this.platformDetector.currentPlatform === PlatformType.ANDROID) {
                await this.initializeAndroidListeners();
            }
        } catch (error) {
            console.error('❌ Failed to initialize platform listeners:', this.stringifyError(error));
        }
    }

    /**
     * Initialize Android-specific event listeners
     */
    private async initializeAndroidListeners(): Promise<void> {
        try {
            if (typeof window !== 'undefined' && (window as any).Capacitor) {
                const { MedicalDeviceCommunicationPlugin } = await import('../plugins/medical-device-communication.plugin');

                // Add listeners for device results
                await MedicalDeviceCommunicationPlugin.addListener('DeviceResult', (data: any) => {
                    console.log('📥 DeviceResult event received:', data);
                    this.handleAndroidDeviceResult(data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('PulseOximeterResult', (data: any) => {
                    console.log('📥 PulseOximeterResult event received:', data);
                    this.handleAndroidPulseOximeterResult(data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('StethoscopeResult', (data: any) => {
                    console.log('📥 StethoscopeResult event received:', data);
                    this.handleAndroidDeviceResult(data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('ThermometerResult', (data: any) => {
                    console.log('📥 ThermometerResult event received:', data);
                    this.handleAndroidDeviceResult(data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('BloodPressureResult', (data: any) => {
                    console.log('📥 BloodPressureResult event received:', data);
                    this.handleAndroidDeviceResult(data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('ECGResult', (data: any) => {
                    console.log('📥 ECGResult event received:', data);
                    this.handleAndroidDeviceResult(data);
                });

                console.log('✅ Android event listeners initialized');
            }
        } catch (error) {
            console.error('❌ Failed to initialize Android listeners:', this.stringifyError(error));
        }
    }

    /**
     * Handle Android device result events
     */
    private handleAndroidDeviceResult(data: any): void {
        try {
            console.log('🔄 Processing Android device result:', data);

            // Validate data structure
            if (!data) {
                console.warn('⚠️ Received null or undefined device result');
                return;
            }

            // Extract device type
            const deviceType = data.deviceType || DeviceType.PULSE_OXIMETER;

            // Create standardized result
            const result: DeviceCommunicationResult = {
                success: data.success !== false,
                data: this.processAndroidDeviceData(deviceType, data.data),
                platform: PlatformType.ANDROID,
                deviceType: deviceType,
                timestamp: data.timestamp || Date.now(),
                error: data.error
            };

            // Update connection status
            if (result.success) {
                this.updateConnectionStatus(deviceType, 'connected');
            } else {
                this.updateConnectionStatus(deviceType, 'error');
            }

            // Emit result to subscribers
            this.deviceData$.next(result);

            console.log('✅ Android device result processed successfully');

        } catch (error) {
            console.error('❌ Error processing Android device result:', this.stringifyError(error));

            // Emit error result
            const errorResult: DeviceCommunicationResult = {
                success: false,
                error: `Failed to process device result: ${error instanceof Error ? error.message : 'Unknown error'}`,
                platform: PlatformType.ANDROID,
                deviceType: DeviceType.PULSE_OXIMETER,
                timestamp: Date.now()
            };

            this.deviceData$.next(errorResult);
        }
    }

    /**
     * Handle Android pulse oximeter result specifically
     */
    private handleAndroidPulseOximeterResult(data: any): void {
        try {
            console.log('🫀 Processing Android pulse oximeter result:', data);

            // Enhanced validation with null checks
            if (!data) {
                console.warn('⚠️ Received null pulse oximeter result data');
                // Create default result to prevent undefined errors
                const defaultResult: DeviceCommunicationResult<PulseOximeterData> = {
                    success: false,
                    data: {
                        spo2: 0,
                        pulseRate: 0,
                        timestamp: Date.now()
                    },
                    platform: PlatformType.ANDROID,
                    deviceType: DeviceType.PULSE_OXIMETER,
                    timestamp: Date.now(),
                    error: 'No data received from device'
                };
                this.deviceData$.next(defaultResult);
                return;
            }

            // Process pulse oximeter data with comprehensive null checks
            const pulseOximeterData = this.processPulseOximeterData(data.data || data);

            // Create standardized result
            const result: DeviceCommunicationResult<PulseOximeterData> = {
                success: data.success !== false,
                data: pulseOximeterData,
                platform: PlatformType.ANDROID,
                deviceType: DeviceType.PULSE_OXIMETER,
                timestamp: data.timestamp || Date.now(),
                error: data.error
            };

            // Update connection status
            this.updateConnectionStatus(DeviceType.PULSE_OXIMETER, 'connected');

            // Emit result to subscribers
            this.deviceData$.next(result);

            console.log('✅ Pulse oximeter result processed successfully:', pulseOximeterData);

        } catch (error) {
            console.error('❌ Error processing pulse oximeter result:', this.stringifyError(error));

            // Emit error result with safe data
            const errorResult: DeviceCommunicationResult = {
                success: false,
                error: `Failed to process pulse oximeter result: ${error instanceof Error ? error.message : 'Unknown error'}`,
                platform: PlatformType.ANDROID,
                deviceType: DeviceType.PULSE_OXIMETER,
                timestamp: Date.now()
            };

            this.deviceData$.next(errorResult);
        }
    }

    /**
     * Process Android device data with proper validation
     */
    private processAndroidDeviceData(deviceType: DeviceType, rawData: any): any {
        if (!rawData) {
            console.warn('⚠️ No data to process for device:', deviceType);
            return null;
        }

        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                return this.processPulseOximeterData(rawData);
            case DeviceType.STETHOSCOPE:
                return this.processStethoscopeData(rawData);
            case DeviceType.THERMOMETER:
                return this.processThermometerData(rawData);
            case DeviceType.BLOOD_PRESSURE:
                return this.processBloodPressureData(rawData);
            case DeviceType.ECG:
                return this.processECGData(rawData);
            default:
                return rawData;
        }
    }

    /**
     * Process pulse oximeter data with enhanced validation
     */
    private processPulseOximeterData(data: any): PulseOximeterData {
        // Enhanced null checks to prevent "Cannot read properties of undefined" errors
        if (!data) {
            console.warn('⚠️ Pulse oximeter data is null or undefined, using default values');
            return {
                spo2: 0,
                pulseRate: 0,
                timestamp: Date.now()
            };
        }

        // Safe property access with fallbacks
        const spo2 = this.parseNumericValue(data?.spo2, 0) || 0;
        const pulseRate = this.parseNumericValue(data?.pulse_rate || data?.pulseRate, 0) || 0;
        const batteryLevel = this.parseNumericValue(data?.battery_level, undefined);
        const signalQuality = this.parseNumericValue(data?.signal_quality, undefined);

        const result: PulseOximeterData = {
            spo2,
            pulseRate,
            timestamp: Date.now(),
            ...(batteryLevel !== undefined && { batteryLevel }),
            ...(signalQuality !== undefined && { signalQuality })
        };

        console.log('📊 Processed pulse oximeter data with null safety:', result);
        return result;
    }

    /**
     * Process stethoscope data with validation
     */
    private processStethoscopeData(data: any): StethoscopeData {
        if (!data) {
            console.warn('⚠️ Stethoscope data is null, using defaults');
            return {
                audioData: new ArrayBuffer(0),
                duration: 0,
                sampleRate: 44100,
                timestamp: Date.now()
            };
        }

        const duration = this.parseNumericValue(data?.duration, 0) || 0;
        const sampleRate = this.parseNumericValue(data?.sample_rate, 44100) || 44100;
        const heartRate = this.parseNumericValue(data?.heart_rate, undefined);

        return {
            audioData: data?.audio_data || new ArrayBuffer(0),
            duration,
            sampleRate,
            timestamp: Date.now(),
            ...(heartRate !== undefined && { heartRate })
        };
    }

    /**
     * Process thermometer data with validation
     */
    private processThermometerData(data: any): ThermometerData {
        if (!data) {
            console.warn('⚠️ Thermometer data is null, using defaults');
            return {
                temperature: 0,
                unit: 'celsius',
                timestamp: Date.now(),
                source: 'icare'  // 👈 Add this
            };

        }

        const temperature = this.parseNumericValue(data?.temperature, 0) || 0;
        const batteryLevel = this.parseNumericValue(data?.battery_level, undefined);

        return {
            temperature: Math.round(temperature * 10) / 10,
            unit: data?.unit || 'celsius',
            timestamp: Date.now(),
            batteryLevel: data?.batteryLevel,
            source: 'icare', // ✅ Required by ThermometerData interface
            sensorType: data?.sensor_type // ✅ Optional if your interface allows it
        };


    }

    /**
     * Process blood pressure data with validation
     */
    private processBloodPressureData(data: any): BloodPressureData {
        if (!data) {
            console.warn('⚠️ Blood pressure data is null, using defaults');
            return {
                systolic: 0,
                diastolic: 0,
                pulse: 0,
                timestamp: Date.now()
            };
        }

        const systolic = this.parseNumericValue(data?.systolic, 0) || 0;
        const diastolic = this.parseNumericValue(data?.diastolic, 0) || 0;
        const pulse = this.parseNumericValue(data?.pulse, 0) || 0;
        const batteryLevel = this.parseNumericValue(data?.battery_level, undefined);

        return {
            systolic,
            diastolic,
            pulse,
            timestamp: Date.now(),
            ...(batteryLevel !== undefined && { batteryLevel })
        };
    }

    /**
     * Process ECG data with validation
     */
    private processECGData(data: any): ECGData {
        if (!data) {
            console.warn('⚠️ ECG data is null, using defaults');
            return {
                waveformData: [],
                heartRate: 0,
                duration: 0,
                timestamp: Date.now(),
                leads: []
            };
        }

        const heartRate = this.parseNumericValue(data?.heart_rate, 0) || 0;
        const duration = this.parseNumericValue(data?.duration, 0) || 0;

        return {
            waveformData: Array.isArray(data?.waveform_data) ? data.waveform_data : [],
            heartRate,
            duration,
            timestamp: Date.now(),
            leads: Array.isArray(data?.leads) ? data.leads : []
        };
    }

    /**
     * Safely parse numeric values with fallback
     */
    private parseNumericValue(value: any, fallback: number | undefined): number | undefined {
        if (value === null || value === undefined || value === '') {
            return fallback;
        }

        const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
        return isNaN(parsed) ? fallback : parsed;
    }

    /**
     * Safely stringify errors for logging
     */
    private stringifyError(error: any): string {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'object' && error !== null) {
            try {
                return JSON.stringify(error);
            } catch {
                return String(error);
            }
        }
        return String(error);
    }

    /**
     * Handle data from provider
     */
    private handleProviderData(data: DeviceCommunicationResult): void {
        console.log('📥 Provider data received:', data);

        // Update connection status based on data
        if (data.success) {
            this.updateConnectionStatus(data.deviceType, 'connected');
        } else {
            this.updateConnectionStatus(data.deviceType, 'error');
        }

        // Forward data to subscribers
        this.deviceData$.next(data);
    }

    /**
     * Handle connection status updates from provider
     */
    private handleConnectionStatusUpdate(statusMap: Map<DeviceType, boolean>): void {
        statusMap.forEach((isConnected, deviceType) => {
            const status: DeviceConnectionStatus = isConnected ? 'connected' : 'disconnected';
            this.updateConnectionStatus(deviceType, status);
        });
    }

    /**
     * Update connection status for a device
     */
    private updateConnectionStatus(deviceType: DeviceType, status: DeviceConnectionStatus): void {
        const currentStatus = this.connectionStatus$.value;
        const updatedStatus = new Map(currentStatus);
        updatedStatus.set(deviceType, status);
        this.connectionStatus$.next(updatedStatus);
    }

    /**
     * Generate unique session ID
     */
    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Cleanup on service destroy
     */
    ngOnDestroy(): void {
        console.log('🧹 Cleaning up Medical Device Communication Service');

        this.destroy$.next();
        this.destroy$.complete();

        // Disconnect all active connections
        this.activeConnections.forEach(async (sessionId, deviceType) => {
            try {
                await this.disconnectDevice(deviceType);
            } catch (error) {
                console.error(`❌ Error disconnecting ${deviceType}:`, this.stringifyError(error));
            }
        });

        this.activeConnections.clear();
    }
}

/**
 * Device connection status type
 */
export type DeviceConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

/**
 * Platform information interface
 */
export interface PlatformInfo {
    platform: PlatformType;
    capabilities: any;
    config: any;
}
