# NovaICare Integration Guide
## Enterprise Medical Device Communication Solution

### Overview
This guide provides comprehensive instructions for integrating with the NovaICare medical device application using the updated enterprise-level communication solution. The implementation has been aligned with the actual NovaICare reference implementation to ensure seamless compatibility.

---

## 🔧 Updated Configuration

### Device Activity Mappings (From Reference Implementation)
```typescript
// Updated to match actual NovaICare activities
PULSE_OXIMETER: "com.neurosynaptic.ble.sensors.PulseOxiMeterSensor1"
STETHOSCOPE: "com.neurosynaptic.usb.StethoSensor"
THERMOMETER: "com.neurosynaptic.ble.sensors.TemperatureSensor1"
BLOOD_PRESSURE: "com.neurosynaptic.ble.sensors.BloodPressureSensor1"
ECG: "com.neurosynaptic.bluetooth.sensors.ECGSensor1"
```

### Intent Extras (Exact Format from Reference)
```typescript
// Standard NovaICare intent extras
{
  "use_sensor": true,           // Note: lowercase key
  "class_name": "MainActivity", // Your app's main activity
  "package_name": "io.ionic.starter", // Your app's package
  "language": "en",
  "useflag": "0",
  "pid": "patient_id"
}
```

---

## 📱 Device-Specific Result Keys

### Pulse Oximeter Results
```typescript
{
  "spo2": "98",
  "pulse_rate": "72",
  "error": null,
  "file_name": "spo2_reading.dat",
  "sensor_type": "Pulse Oximeter"
}
```

### Thermometer Results
```typescript
{
  "celcius": "36.5",
  "fahrenheit": "97.7",
  "sensor_type": "Thermometer"
}
```

### Blood Pressure Results
```typescript
{
  "systolic": "120",
  "diastolic": "80",
  "pulse_rate": "72",
  "error": null,
  "sensor_type": "Blood Pressure"
}
```

### Stethoscope Results
```typescript
{
  "Stetho_Reading": "audio_file.wav",
  "stetho_value": "heart_sound_data",
  "sensor_type": "StethoScope"
}
```

### ECG Results
```typescript
{
  "pulse_rate": "72",
  "ecg_lead1": "lead1_data",
  "ecg_lead2": "lead2_data",
  "ecg_lead3": "lead3_data",
  "ecg_avr": "avr_data",
  "ecg_avl": "avl_data",
  "ecg_avf": "avf_data",
  "ecg_v1": "v1_data",
  "ecg_v2": "v2_data",
  "ecg_v3": "v3_data",
  "ecg_v4": "v4_data",
  "ecg_v5": "v5_data",
  "ecg_v6": "v6_data",
  "file_name": "ecg_reading.dat",
  "ecg_value": "ecg_processed_data",
  "sensor_type": "ECG"
}
```

---

## 🚀 Implementation Examples

### 1. Basic Device Launch
```typescript
import { MedicalDeviceCommunicationService } from './core/services/medical-device-communication.service';
import { DeviceType } from './core/interfaces/medical-device.interface';

// Inject the service
constructor(private deviceService: MedicalDeviceCommunicationService) {}

// Launch pulse oximeter
async launchPulseOximeter() {
  try {
    const result = await this.deviceService.launchDevice({
      deviceType: DeviceType.PULSE_OXIMETER,
      patientId: 'PATIENT_001',
      realId: 'REAL_001',
      language: 'en'
    });

    if (result.success) {
      console.log('SPO2:', result.data.spo2);
      console.log('Pulse Rate:', result.data.pulse_rate);
    }
  } catch (error) {
    console.error('Device launch failed:', error);
  }
}
```

### 2. Advanced Usage with Listeners
```typescript
async setupDeviceWithListener() {
  // Add listener for real-time data
  const listenerId = this.deviceService.addDeviceListener(
    DeviceType.PULSE_OXIMETER,
    (result) => {
      if (result.success) {
        this.updateUI(result.data);
      } else {
        this.handleError(result.error);
      }
    }
  );

  // Launch device
  await this.deviceService.launchDevice({
    deviceType: DeviceType.PULSE_OXIMETER,
    patientId: 'PATIENT_001'
  });

  // Clean up listener when done
  // this.deviceService.removeDeviceListener(listenerId);
}
```

### 3. Multi-Device Session
```typescript
async conductFullExamination() {
  const devices = [
    DeviceType.PULSE_OXIMETER,
    DeviceType.THERMOMETER,
    DeviceType.BLOOD_PRESSURE
  ];

  const results = [];

  for (const deviceType of devices) {
    try {
      const result = await this.deviceService.launchDevice({
        deviceType,
        patientId: 'PATIENT_001',
        language: 'en'
      });

      if (result.success) {
        results.push({
          device: deviceType,
          data: result.data,
          timestamp: result.timestamp
        });
      }
    } catch (error) {
      console.error(`Failed to launch ${deviceType}:`, error);
    }
  }

  return results;
}
```

---

## 🔧 Android Native Integration

### MainActivity.java Updates
```java
@Override
protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    
    // Handle NovaICare results
    if (requestCode == NOVA_ICARE_REQUEST_CODE) {
        if (resultCode == RESULT_OK && data != null) {
            // Parse result using NovaICareIntentService
            NovaICareIntentService.DeviceResult result = 
                novaICareService.parseIntentResult(data, expectedDeviceType);
            
            // Send result back to Ionic
            sendResultToIonic(result);
        }
    }
}
```

### Capacitor Plugin Integration
```java
@CapacitorPlugin(name = "MedicalDeviceCommunication")
public class MedicalDeviceCommunicationPlugin extends Plugin {
    
    @PluginMethod
    public void launchDevice(PluginCall call) {
        try {
            String deviceType = call.getString("deviceType");
            String patientId = call.getString("patientId");
            
            // Create launch request
            NovaICareIntentService.DeviceLaunchRequest request = 
                new NovaICareIntentService.DeviceLaunchRequest(
                    deviceType,
                    "MainActivity",
                    getContext().getPackageName(),
                    "en",
                    patientId,
                    generateSessionId(),
                    null
                );
            
            // Create and launch intent
            Intent intent = novaICareService.createDeviceIntent(request);
            getActivity().startActivityForResult(intent, NOVA_ICARE_REQUEST_CODE);
            
        } catch (Exception e) {
            call.reject("Failed to launch device: " + e.getMessage());
        }
    }
}
```

---

## 🌐 Web/Desktop Fallback

### WebSocket Configuration
```typescript
// Updated WebSocket endpoints for Jetty server
const WEBSOCKET_CONFIG = {
  development: {
    url: 'ws://localhost:8444',
    endpoints: {
      PULSE_OXIMETER: '/bleWS/',
      STETHOSCOPE: '/audioWS/',
      THERMOMETER: '/tempWS/',
      BLOOD_PRESSURE: '/bpWS/',
      ECG: '/ecgWS/'
    }
  },
  production: {
    url: 'wss://s3test2.remedi.co.in:8444',
    // ... same endpoints
  }
};
```

### WebSocket Usage
```typescript
// Automatic fallback for web/desktop platforms
async launchDeviceWebSocket() {
  const wsUrl = this.deviceService.getWebSocketUrl(
    DeviceType.PULSE_OXIMETER,
    'development'
  );
  
  const connection = await this.deviceService.connectWebSocket(wsUrl);
  
  connection.onMessage((data) => {
    console.log('Device data received:', data);
  });
}
```

---

## 🛠️ Troubleshooting

### Common Issues

#### 1. NovaICare App Not Found
```typescript
// Check if NovaICare is installed
const isInstalled = await this.deviceService.isNovaICareInstalled();
if (!isInstalled) {
  // Guide user to install NovaICare
  this.showInstallationGuide();
}
```

#### 2. Intent Launch Failures
```typescript
// Validate device support before launch
const isSupported = await this.deviceService.isDeviceSupported(
  DeviceType.PULSE_OXIMETER
);

if (!isSupported) {
  console.error('Device not supported on this platform');
}
```

#### 3. Result Parsing Issues
```typescript
// Enable debug logging
MedicalDeviceConfig.LOGGING_ENABLED = true;

// Check result format
console.log('Raw result:', result);
console.log('Parsed data:', result.data);
```

---

## 📋 Testing Checklist

### Pre-Deployment Testing
- [ ] NovaICare app installed on test device
- [ ] All device types launch successfully
- [ ] Intent extras match reference format
- [ ] Result parsing handles all data fields
- [ ] Error handling works for edge cases
- [ ] WebSocket fallback functional for web/desktop
- [ ] Memory leaks checked for long sessions
- [ ] Performance tested with multiple devices

### Production Validation
- [ ] Package name matches production app
- [ ] Activity names verified with NovaICare team
- [ ] SSL certificates configured for WebSocket
- [ ] Error reporting integrated
- [ ] User permissions handled gracefully
- [ ] Offline scenarios tested

---

## 🔐 Security Considerations

### Data Protection
```typescript
// Encrypt sensitive patient data
const encryptedPatientId = await this.encryptionService.encrypt(patientId);

// Use secure WebSocket connections in production
const wsConfig = MedicalDeviceConfig.getWebSocketConfig('production');
// Uses wss:// protocol automatically
```

### Permission Management
```xml
<!-- Android permissions for device access -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

---

## 📞 Support

### Integration Support
- **Technical Issues**: Check logs with debug mode enabled
- **NovaICare Compatibility**: Verify activity names and intent format
- **Performance Issues**: Monitor memory usage and connection pooling

### Reference Implementation
- All configurations match the provided NovaICare reference files
- Intent structure follows exact format from working Android implementation
- Result parsing handles all documented data fields

---

## 🔄 Version Compatibility

| Component | Version | NovaICare Compatibility |
|-----------|---------|------------------------|
| Android Intent Service | 2.0.0 | ✅ Reference Aligned |
| WebSocket Service | 2.0.0 | ✅ Jetty Compatible |
| Capacitor Plugin | 2.0.0 | ✅ Latest Capacitor |
| Configuration | 2.0.0 | ✅ Production Ready |

---

This integration guide ensures seamless compatibility with the actual NovaICare implementation while providing enterprise-level reliability and maintainability.
