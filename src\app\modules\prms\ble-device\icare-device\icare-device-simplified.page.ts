import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgZone } from '@angular/core';
import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { App } from '@capacitor/app';
import { DeviceDataManagerService } from 'src/app/core/services/device-data-manager.service';
import {
    DeviceType,
    PulseOximeterData,
    ThermometerData,
    BloodPressureData,
    ECGData,
    StethoscopeData
} from 'src/app/core/interfaces/medical-device.interface';
import { Subscription } from 'rxjs';

@Component({
    selector: 'app-icare-device',
    templateUrl: './icare-device.page.html',
    styleUrls: ['./icare-device.page.scss'],
    standalone: true,
    imports: [IonicModule, CommonModule, FormsModule, DatePipe, TitleCasePipe]
})
export class IcareDevicePage implements OnInit, On<PERSON><PERSON>roy {
    // Device data properties
    resultdata: any;
    thermometerData: ThermometerData | null = null;
    bloodPressureData: BloodPressureData | null = null;
    ecgData: ECGData | null = null;
    stethoscopeData: StethoscopeData | null = null;

    // Available devices tracking
    availableDevices: DeviceType[] = [];
    isLoadingDevices = true;

    // SINGLE subscription array for centralized management
    private subscriptions: Subscription[] = [];
    private appStateSubscription?: any;

    constructor(
        private ngZone: NgZone,
        private deviceDataManager: DeviceDataManagerService
    ) { }

    async ngOnInit() {
        console.log('🚀 IcareDevicePage initialized - using ONLY centralized device management');

        // Load available devices first
        await this.loadAvailableDevices();

        // Register ONLY centralized device listeners (single source of truth)
        this.registerCentralizedDeviceListeners();

        // Register app state listener
        this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
            console.log('📱 App state changed. isActive:', isActive);
            if (isActive) {
                console.log('✅ App is active again → re-registering centralized listeners');
                this.registerCentralizedDeviceListeners();
            }
        });
    }

    ngOnDestroy() {
        console.log('🧹 IcareDevicePage destroyed - cleaning up listeners');

        // Clean up all subscriptions
        this.subscriptions.forEach(sub => sub.unsubscribe());
        this.subscriptions = [];

        if (this.appStateSubscription) {
            this.appStateSubscription.remove();
        }
    }

    /**
     * Load available devices from the centralized device manager
     */
    async loadAvailableDevices() {
        try {
            console.log('🔍 Loading available devices...');
            this.isLoadingDevices = true;

            this.availableDevices = await this.deviceDataManager.getAvailableDevices();

            console.log('✅ Available devices loaded:', this.availableDevices);
            this.isLoadingDevices = false;
        } catch (error) {
            console.error('❌ Error loading available devices:', error);
            this.isLoadingDevices = false;

            // Fallback to showing pulse oximeter (known to work)
            this.availableDevices = [DeviceType.PULSE_OXIMETER];
        }
    }

    /**
     * Register ONLY centralized device data listeners
     * This is the SINGLE source of truth for all device data
     * Removed all other listeners to prevent conflicts
     */
    registerCentralizedDeviceListeners() {
        try {
            console.log('🔗 Registering centralized device data listeners (SINGLE source of truth)');

            // Clear existing subscriptions to avoid duplicates
            this.subscriptions.forEach(sub => sub.unsubscribe());
            this.subscriptions = [];

            // Subscribe to pulse oximeter data
            const pulseOximeterSub = this.deviceDataManager.getDeviceData<PulseOximeterData>(DeviceType.PULSE_OXIMETER)
                .subscribe({
                    next: (data) => {
                        if (data) {
                            this.ngZone.run(() => {
                                this.resultdata = {
                                    spo2: data.spo2 || 0,
                                    pulse_rate: data.pulseRate || 0,
                                    timestamp: data.timestamp || Date.now(),
                                    batteryLevel: data.batteryLevel,
                                    signalQuality: data.signalQuality,
                                    source: 'CentralizedManager'
                                };
                                console.log('✅ Pulse oximeter data updated (centralized):', this.resultdata);
                            });
                        }
                    },
                    error: (error) => {
                        console.error('❌ Error in pulse oximeter subscription:', error);
                        this.ngZone.run(() => {
                            this.resultdata = {
                                error: 'Failed to get pulse oximeter data',
                                timestamp: Date.now(),
                                source: 'CentralizedManager'
                            };
                        });
                    }
                });

            // Subscribe to thermometer data
            const thermometerSub = this.deviceDataManager.getDeviceData<ThermometerData>(DeviceType.THERMOMETER)
                .subscribe({
                    next: (data) => {
                        this.ngZone.run(() => {
                            this.thermometerData = data;
                            console.log('🌡️ Thermometer data updated (centralized):', data);
                        });
                    },
                    error: (error) => {
                        console.error('❌ Error in thermometer subscription:', error);
                        this.ngZone.run(() => {
                            this.thermometerData = null;
                        });
                    }
                });

            // Subscribe to blood pressure data
            const bloodPressureSub = this.deviceDataManager.getDeviceData<BloodPressureData>(DeviceType.BLOOD_PRESSURE)
                .subscribe({
                    next: (data) => {
                        this.ngZone.run(() => {
                            this.bloodPressureData = data;
                            console.log('🩺 Blood pressure data updated (centralized):', data);
                        });
                    },
                    error: (error) => {
                        console.error('❌ Error in blood pressure subscription:', error);
                        this.ngZone.run(() => {
                            this.bloodPressureData = null;
                        });
                    }
                });

            // Subscribe to ECG data
            const ecgSub = this.deviceDataManager.getDeviceData<ECGData>(DeviceType.ECG)
                .subscribe({
                    next: (data) => {
                        this.ngZone.run(() => {
                            this.ecgData = data;
                            console.log('📈 ECG data updated (centralized):', data);
                        });
                    },
                    error: (error) => {
                        console.error('❌ Error in ECG subscription:', error);
                        this.ngZone.run(() => {
                            this.ecgData = null;
                        });
                    }
                });

            // Subscribe to stethoscope data
            const stethoscopeSub = this.deviceDataManager.getDeviceData<StethoscopeData>(DeviceType.STETHOSCOPE)
                .subscribe({
                    next: (data) => {
                        this.ngZone.run(() => {
                            this.stethoscopeData = data;
                            console.log('🔊 Stethoscope data updated (centralized):', data);
                        });
                    },
                    error: (error) => {
                        console.error('❌ Error in stethoscope subscription:', error);
                        this.ngZone.run(() => {
                            this.stethoscopeData = null;
                        });
                    }
                });

            // Store subscriptions for cleanup
            this.subscriptions = [
                pulseOximeterSub,
                thermometerSub,
                bloodPressureSub,
                ecgSub,
                stethoscopeSub
            ];

            console.log('✅ Centralized device listeners registered successfully (NO other listeners)');
        } catch (error) {
            console.error('❌ Error registering centralized device listeners:', error);
        }
    }

    // Device launch methods - all use centralized manager
    async launchPulseOximeter() {
        console.log('🚀 Launching Pulse Oximeter via Centralized Manager...');
        try {
            const result = await this.deviceDataManager.launchDevice(
                DeviceType.PULSE_OXIMETER,
                '12345',
                { language: 'en' }
            );

            console.log('✅ Pulse oximeter launch result:', result);

            if (!result.success) {
                console.error('❌ Failed to launch pulse oximeter:', result.error);
                alert('Failed to launch pulse oximeter: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error launching pulse oximeter:', error);
            alert('Error launching pulse oximeter: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    }

    async launchThermometer() {
        console.log('🌡️ Launching Thermometer via Centralized Manager...');
        try {
            const result = await this.deviceDataManager.launchDevice(
                DeviceType.THERMOMETER,
                '12345',
                { language: 'en' }
            );

            console.log('✅ Thermometer launch result:', result);

            if (!result.success) {
                console.error('❌ Failed to launch thermometer:', result.error);
                alert('Failed to launch thermometer: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error launching thermometer:', error);
            alert('Error launching thermometer: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    }

    async launchBloodPressure() {
        console.log('🩺 Launching Blood Pressure Monitor via Centralized Manager...');
        try {
            const result = await this.deviceDataManager.launchDevice(
                DeviceType.BLOOD_PRESSURE,
                '12345',
                { language: 'en' }
            );

            console.log('✅ Blood pressure monitor launch result:', result);

            if (!result.success) {
                console.error('❌ Failed to launch blood pressure monitor:', result.error);
                alert('Failed to launch blood pressure monitor: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error launching blood pressure monitor:', error);
            alert('Error launching blood pressure monitor: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    }

    async launchECG() {
        console.log('📈 Launching ECG via Centralized Manager...');
        try {
            const result = await this.deviceDataManager.launchDevice(
                DeviceType.ECG,
                '12345',
                { language: 'en' }
            );

            console.log('✅ ECG launch result:', result);

            if (!result.success) {
                console.error('❌ Failed to launch ECG:', result.error);
                alert('Failed to launch ECG: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error launching ECG:', error);
            alert('Error launching ECG: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    }

    async launchStethoscope() {
        console.log('🔊 Launching Stethoscope via Centralized Manager...');
        try {
            const result = await this.deviceDataManager.launchDevice(
                DeviceType.STETHOSCOPE,
                '12345',
                { language: 'en' }
            );

            console.log('✅ Stethoscope launch result:', result);

            if (!result.success) {
                console.error('❌ Failed to launch stethoscope:', result.error);
                alert('Failed to launch stethoscope: ' + result.error);
            }
        } catch (error) {
            console.error('❌ Error launching stethoscope:', error);
            alert('Error launching stethoscope: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    }

    /**
     * Clear data for specific device type
     */
    clearDeviceData(deviceType: DeviceType) {
        console.log(`🧹 Clearing data for ${deviceType}`);
        this.deviceDataManager.clearDeviceData(deviceType);

        // Also clear local data
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                this.resultdata = null;
                break;
            case DeviceType.THERMOMETER:
                this.thermometerData = null;
                break;
            case DeviceType.BLOOD_PRESSURE:
                this.bloodPressureData = null;
                break;
            case DeviceType.ECG:
                this.ecgData = null;
                break;
            case DeviceType.STETHOSCOPE:
                this.stethoscopeData = null;
                break;
        }
    }

    /**
     * Clear all device data
     */
    clearAllDeviceData() {
        console.log('🧹 Clearing all device data');
        this.deviceDataManager.clearAllDeviceData();

        // Clear local data
        this.resultdata = null;
        this.thermometerData = null;
        this.bloodPressureData = null;
        this.ecgData = null;
        this.stethoscopeData = null;
    }

    /**
     * Get current platform info
     */
    getCurrentPlatform(): string {
        return this.deviceDataManager.getCurrentPlatform();
    }

    /**
     * Check if device is supported
     */
    isDeviceSupported(deviceType: DeviceType): boolean {
        return this.deviceDataManager.isDeviceSupported(deviceType);
    }

    /**
     * Check if device is available (for template use)
     */
    isDeviceAvailable(deviceType: string): boolean {
        const deviceTypeEnum = deviceType as DeviceType;
        return this.availableDevices.includes(deviceTypeEnum);
    }
}
