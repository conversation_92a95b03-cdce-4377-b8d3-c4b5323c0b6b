.hemoglobin-dialog-container {
    // padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    text-align: center;
    font-family: 'Segoe UI', sans-serif;
    width: 100%;
    .modal-header h3 {
        margin-bottom: 10px;
        font-size: 22px;
        color: #333;
    }
    .instruction-text {
        font-size: 14px;
        text-align: left;
        margin: 0 auto 20px;
        max-width: 400px;
        ol {
            padding-left: 20px;
            margin-bottom: 10px;
        }
        input {
            display: block;
            margin: 10px auto;
            padding: 8px;
            width: 200px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    }
    .center {
        display: flex;
        justify-content: center;
    }
    .take-reading-btn {
        background-color: #fcbf49;
        border: none;
        padding: 10px 20px;
        color: black;
        font-weight: bold;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }
    .manual-entry {
        margin-top: 20px;
        font-size: 14px;
        b {
            display: block;
            margin-bottom: 10px;
        }
        .entry-group {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            input {
                padding: 8px;
                width: 180px;
                font-size: 14px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            span {
                font-weight: bold;
                color: #555;
            }
        }
    }
    .modal-footer {
        margin-top: 20px;
        padding-bottom: 10px;
        .save-btn {
            background-color: #0d6efd;
            border: none;
            padding: 8px 20px;
            color: #fff;
            font-weight: bold;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
        }
    }
    .status-message {
        padding: 10px;
        margin: 10px auto;
        max-width: 80%;
        border-radius: 5px;
        font-weight: bold;
        text-align: center;
        &.success-msg {
            background-color: #d4edda;
            color: #155724;
        }
        &.error-msg {
            background-color: #f8d7da;
            color: #721c24;
        }
    }
}

.spo2-header {
    background-color: #007bff;
    color: white;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    position: relative;
}