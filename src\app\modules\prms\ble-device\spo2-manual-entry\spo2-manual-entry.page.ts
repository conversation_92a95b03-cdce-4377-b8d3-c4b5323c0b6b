import {
  Component,
  OnInit,
  Inject,
  OnD<PERSON>roy
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormsModule
} from '@angular/forms';
import {
  Router,
  RouterModule
} from '@angular/router';
import {
  IonicModule,
  ModalController
} from '@ionic/angular';
import {
  Subscription,
  Observable
} from 'rxjs';
import {
  MatDialog,
  MatDialogRef,
  MatDialogModule,
  MAT_DIALOG_DATA
} from '@angular/material/dialog';
import {
  WebsocketsService
} from 'src/app/core/services/websocket.service';
import {
  ApiService
} from 'src/app/core/services/api.service';
import {
  ConstantService
} from 'src/app/core/services/constant.service';
import {
  AuthService
} from 'src/app/core/services/auth.service';
import { CommonModule, NgClass  } from '@angular/common';


interface ApiResponse {
  status: string;
  message?: string;
  [key: string]: any;
}

@Component({
  selector: 'app-spo2-manual-entry',
  templateUrl: './spo2-manual-entry.page.html',
  styleUrls: ['./spo2-manual-entry.page.scss'],
  standalone: true,
  imports: [
  IonicModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatDialogModule,
    NgClass,
    CommonModule
  ]
})
export class Spo2ManualEntryPage implements OnInit, OnDestroy {
  batchCode: string = '';
  manualResult: number | null = null;
  hemoglobin: number = 0;

  resultMessage: string = '';
  isSuccess: boolean = false;

  showBatchCodeUI = true;

  subscriptionInit!: Subscription;

  getPatientData: any;
  currDomainId: any;
  tokenRequest: string = '';
  msgStatus: number = 0;
  constructor(
    private modalCtrl: ModalController,
    private websocketsService: WebsocketsService,
    private router: Router,
    private authService: AuthService,
    private fb: FormBuilder,
    private constantSvc: ConstantService,
    private apiSvc: ApiService,
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<Spo2ManualEntryPage>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    this.tokenRequest = sessionStorage.getItem('token') || '';
    this.currDomainId = JSON.parse(sessionStorage.getItem('domainId') || '0');
    this.getPatientData = JSON.parse(sessionStorage.getItem('patientDetails') || '{}');

    this.websocketsService.sendParamMessage('connectKitFirstTime');

    this.subscriptionInit = this.websocketsService.paramValueObs$.subscribe((data) => {
      this.resultMessage = '';
      this.isSuccess = false;

      if (data?.match(/kitStatusCheck~1001/g)) {
        this.msgStatus = 1;
        this.showBatchCodeUI = true;
      }

      if (data?.match(/ReadHemoglobinValue~0/g)) {
        this.msgStatus = 2;
        this.resultMessage = 'Hemoglobin reading returned 0. Please try again.';
        this.isSuccess = false;
      }

      if (data?.match(/resultUpdate~error1008/g)) {
        this.msgStatus = 2;
        this.resultMessage = 'Device not connected';
        this.isSuccess = false;
        this.showBatchCodeUI = false;
      }

      if (data?.match(/resultUpdate~error1022/g)) {
         this.showBatchCodeUI = false;

        this.msgStatus = 1;
        this.resultMessage = 'Device is ON';
        this.isSuccess = true;
        this.showBatchCodeUI = false;
      }

      if (data?.match(/resultUpdate~Device is incompitable/g)) {

        this.msgStatus = 2;
        this.resultMessage = 'Device incompatible';
        this.isSuccess = false;
        this.showBatchCodeUI = false;
      }

      if (data?.match(/hemoglobinResult/g)) {
          this.showBatchCodeUI = false;
        const parts = data.split('~');
        const result = parseFloat(parts[1]);

        if (!isNaN(result) && result > 0) {
          this.hemoglobin = result;
          this.manualResult = result;
          this.resultMessage = `Hemoglobin Result: ${result} g/dl`;
          this.isSuccess = true;
          this.msgStatus = 1;
        } else {
          this.resultMessage = 'Invalid result from device';
          this.isSuccess = false;
        }
      }
    });
  }

  takeReading(): void {
    this.showBatchCodeUI = false;
    if (this.batchCode.length !== 3) {
      this.resultMessage = 'Please enter a valid 3-digit batch code';
      this.isSuccess = false;
      return;
    }

    this.resultMessage = 'Reading in progress...';
    this.isSuccess = false;
    this.websocketsService.sendParamMessage(`ReadHemoglobinValue~${this.batchCode}`);
  }

  save(): void {
    if (!this.manualResult || this.manualResult <= 0) {
      this.resultMessage = 'Please enter a valid hemoglobin value';
      this.isSuccess = false;
      return;
    }

    const query = `?action=savetoserver&consultationId=${this.getPatientData.consultationId}&patientId=${this.getPatientData.patientId}&domain=${this.currDomainId}&tblname=prms_parameters&paramName=hemoglobin&hemoglobin=${this.manualResult}&language=English&requestFrom=angular&token=${this.tokenRequest}`;

    (this.apiSvc.postServiceByQueryBasic(this.constantSvc.APIConfig.DISPLAYAPPOINTMENTS, query) as Observable<ApiResponse>)
      .subscribe((res: ApiResponse) => {
        if (res.status === 'success') {
          this.resultMessage = 'Hemoglobin value saved successfully!';
          this.isSuccess = true;
          setTimeout(() => this.dialogRef.close(), 1500);
        } else {
          this.resultMessage = 'Failed to save hemoglobin value';
          this.isSuccess = false;
        }
      });
  }

  ngOnDestroy(): void {
    this.subscriptionInit?.unsubscribe();
    if (this.msgStatus === 2) {
      this.websocketsService.sendParamMessage('turnOFFHb');
    }
  }

  onDialogClose(): void {
    this.dialogRef.close();
  }
}
