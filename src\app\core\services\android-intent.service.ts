import { Injectable } from '@angular/core';
import {
    DeviceCommunicationProvider,
    DeviceLaunchOptions,
    DeviceCommunicationResult,
    DeviceStatus,
    DeviceType,
    PlatformType,
    PulseOximeterData,
    StethoscopeData,
    ThermometerData,
    BloodPressureData,
    ECGData
} from '../interfaces/medical-device.interface';
import { MedicalDeviceConfig } from '../config/medical-device.config';

/**
 * Android Intent Communication Service
 * Enterprise-level service for Android intent-based communication with NovaICare app
 */
@Injectable({
    providedIn: 'root'
})
export class AndroidIntentService extends DeviceCommunicationProvider {

    readonly platform = PlatformType.ANDROID;
    readonly supportedDevices = [
        DeviceType.PULSE_OXIMETER,
        DeviceType.STETHOSCOPE,
        DeviceType.THERMOMETER,
        DeviceType.BLOOD_PRESSURE,
        DeviceType.ECG
    ];

    private activeConnections = new Map<DeviceType, string>();
    private deviceListeners = new Map<string, (data: any) => void>();

    constructor() {
        super();
        this.initializeIntentHandlers();
    }

    /**
     * Launch device using Android intent
     */
    async launchDevice(options: DeviceLaunchOptions): Promise<DeviceCommunicationResult> {
        try {
            console.log('🚀 Launching Android device:', options);

            // Validate device support
            if (!this.supportedDevices.includes(options.deviceType)) {
                throw new Error(`Device type ${options.deviceType} not supported on Android`);
            }

            // Get device-specific intent configuration
            const intentConfig = MedicalDeviceConfig.getAndroidIntentConfig(options.deviceType);

            // Prepare intent parameters with corrected keys
            const intentParams = this.prepareIntentParameters(options, intentConfig);

            // Log intent parameters for debugging
            console.log('🔧 Intent parameters prepared:', {
                packageName: intentParams.packageName,
                activityName: intentParams.activityName,
                deviceType: options.deviceType,
                patientId: options.patientId || options.realId
            });

            // Generate session ID for tracking
            const sessionId = this.generateSessionId();
            this.activeConnections.set(options.deviceType, sessionId);

            // Launch via Capacitor plugin
            const result = await this.executeAndroidIntent(intentParams);

            return {
                success: true,
                data: result,
                platform: this.platform,
                deviceType: options.deviceType,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ Android intent launch failed:', error);

            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                platform: this.platform,
                deviceType: options.deviceType,
                timestamp: Date.now()
            };
        }
    }

    /**
     * Get device status
     */
    async getDeviceStatus(deviceType: DeviceType): Promise<DeviceStatus> {
        const sessionId = this.activeConnections.get(deviceType);

        return {
            isConnected: !!sessionId,
            lastCommunication: sessionId ? Date.now() : undefined,
            deviceInfo: {
                model: 'NovaICare Device',
                version: '1.0.0'
            }
        };
    }

    /**
     * Disconnect device
     */
    async disconnect(deviceType: DeviceType): Promise<void> {
        const sessionId = this.activeConnections.get(deviceType);

        if (sessionId) {
            this.activeConnections.delete(deviceType);
            this.deviceListeners.delete(sessionId);
            console.log('🔌 Disconnected Android device:', deviceType);
        }
    }

    /**
     * Add listener for device data
     */
    addDeviceListener(
        deviceType: DeviceType,
        callback: (data: DeviceCommunicationResult) => void
    ): string {
        const sessionId = this.activeConnections.get(deviceType) || this.generateSessionId();
        this.deviceListeners.set(sessionId, callback);
        return sessionId;
    }

    /**
     * Remove device listener
     */
    removeDeviceListener(listenerId: string): void {
        this.deviceListeners.delete(listenerId);
    }

    /**
     * Initialize Android intent handlers
     */
    private initializeIntentHandlers(): void {
        // Register global intent result handler
        if (typeof window !== 'undefined' && (window as any).AndroidIntentHandler) {
            (window as any).AndroidIntentHandler.onResult = this.handleIntentResult.bind(this);
        }

        // Initialize Capacitor plugin if available
        this.initializeCapacitorPlugin();
    }

    /**
     * Initialize Capacitor plugin for Android intents
     */
    private async initializeCapacitorPlugin(): Promise<void> {
        try {
            if (typeof window !== 'undefined' && (window as any).Capacitor) {
                const { MedicalDeviceCommunicationPlugin } = await import('../plugins/medical-device-communication.plugin');

                // Add listeners for different device types
                await MedicalDeviceCommunicationPlugin.addListener('PulseOximeterResult', (data: any) => {
                    this.handleDeviceResult(DeviceType.PULSE_OXIMETER, data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('StethoscopeResult', (data: any) => {
                    this.handleDeviceResult(DeviceType.STETHOSCOPE, data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('ThermometerResult', (data: any) => {
                    this.handleDeviceResult(DeviceType.THERMOMETER, data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('BloodPressureResult', (data: any) => {
                    this.handleDeviceResult(DeviceType.BLOOD_PRESSURE, data);
                });

                await MedicalDeviceCommunicationPlugin.addListener('ECGResult', (data: any) => {
                    this.handleDeviceResult(DeviceType.ECG, data);
                });

                console.log('✅ Capacitor plugin initialized for Android intents');
            }
        } catch (error) {
            console.error('❌ Failed to initialize Capacitor plugin:', error);
        }
    }

    /**
     * Prepare intent parameters (Fixed to match MedicalDeviceCommunicationPlugin interface)
     */
    private prepareIntentParameters(options: DeviceLaunchOptions, intentConfig: any): any {
        // Get current app info for callback
        const currentPackageName = 'io.ionic.starter'; // This should be dynamic in real implementation
        const currentClassName = 'io.ionic.starter.MainActivity'; // Full class name required

        // Ensure required parameters are present
        if (!options.patientId && !options.realId) {
            throw new Error('Missing required parameter: patientId or realId must be provided');
        }

        const patientId = options.realId || options.patientId;
        if (!patientId) {
            throw new Error('Missing required parameter: patientId');
        }

        // The Android plugin expects parameters at the top level, not nested in extras
        return {
            // Top-level parameters expected by Android plugin
            deviceType: options.deviceType,
            packageName: intentConfig.packageName || MedicalDeviceConfig.NOVA_ICARE_ANDROID.packageName,
            className: intentConfig.className || MedicalDeviceConfig.NOVA_ICARE_ANDROID.className,
            activityName: intentConfig.activityName,
            language: options.language || 'en',
            patientId: patientId,
            realId: options.realId || '',
            sessionId: options.sessionId || this.generateSessionId(),

            // Extras object for additional parameters
            extras: {
                // Standard NovaICare extras (corrected to match reference exactly)
                'USE_SENSOR': true,  // Always true as per reference
                'class_name': currentClassName,
                'package_name': currentPackageName,
                'language': options.language || 'en',
                'useflag': '0',  // Always '0' as per reference
                'pid': patientId,

                // Additional extras for enhanced compatibility
                'deviceType': options.deviceType,
                'sessionId': options.sessionId || this.generateSessionId(),
                'timestamp': Date.now().toString()
            }
        };
    }

    /**
     * Execute Android intent
     */
    private async executeAndroidIntent(params: any): Promise<any> {
        try {
            // Use the existing plugin or create new one
            if (typeof window !== 'undefined' && (window as any).Capacitor) {
                const { MedicalDeviceCommunicationPlugin } = await import('../plugins/medical-device-communication.plugin');
                return await MedicalDeviceCommunicationPlugin.launchDevice(params);
            } else {
                throw new Error('Capacitor not available for Android intent execution');
            }
        } catch (error) {
            console.error('❌ Android intent execution failed:', error);
            throw error;
        }
    }

    /**
     * Handle intent result from Android (Enhanced error handling)
     */
    private handleIntentResult(result: any): void {
        try {
            console.log('📥 Android intent result received:', result);

            // Validate result structure
            if (!result) {
                console.warn('⚠️ Received null or undefined result');
                return;
            }

            // Check for success/error status
            if (result.success === false) {
                console.warn('⚠️ Device operation failed:', result.error || 'Unknown error');

                // Handle specific error cases
                if (result.cancelled) {
                    console.log('ℹ️ Operation was cancelled by user');
                } else if (result.resultCode) {
                    console.warn('⚠️ Failed with result code:', result.resultCode);
                }
            } else if (result.success === true) {
                console.log('✅ Device operation completed successfully');

                // Log device-specific data
                if (result.data) {
                    console.log('📊 Device data received:', Object.keys(result.data));
                }
            }

            const deviceType = result.deviceType || result.device_type;
            const sessionId = this.activeConnections.get(deviceType);

            if (sessionId && this.deviceListeners.has(sessionId)) {
                const callback = this.deviceListeners.get(sessionId);
                const processedResult = this.processDeviceResult(deviceType, result);

                callback?.({
                    success: result.success !== false,
                    data: processedResult,
                    platform: this.platform,
                    deviceType,
                    timestamp: Date.now()
                });
            } else {
                console.warn('⚠️ No active listener found for device:', deviceType);
            }

        } catch (error) {
            console.error('❌ Error handling Android intent result:', error);

            // Emit error result to prevent hanging listeners
            this.emitErrorResult(error as Error);
        }
    }

    /**
     * Emit error result to all active listeners
     */
    private emitErrorResult(error: Error): void {
        try {
            const errorResult = {
                success: false,
                error: 'Failed to process intent result: ' + error.message,
                timestamp: Date.now()
            };

            // Notify all active listeners about the error
            this.deviceListeners.forEach((callback) => {
                try {
                    callback({
                        ...errorResult,
                        platform: this.platform,
                        deviceType: DeviceType.PULSE_OXIMETER // Default fallback
                    });
                } catch (callbackError) {
                    console.error('❌ Error in listener callback:', callbackError);
                }
            });

        } catch (error) {
            console.error('❌ Error emitting error result:', error);
        }
    }

    /**
     * Handle device-specific results
     */
    private handleDeviceResult(deviceType: DeviceType, data: any): void {
        const sessionId = this.activeConnections.get(deviceType);

        if (sessionId && this.deviceListeners.has(sessionId)) {
            const callback = this.deviceListeners.get(sessionId);
            const processedData = this.processDeviceResult(deviceType, data);

            callback?.({
                success: true,
                data: processedData,
                platform: this.platform,
                deviceType,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Process device-specific result data
     */
    private processDeviceResult(deviceType: DeviceType, rawData: any): any {
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                return this.processPulseOximeterData(rawData);

            case DeviceType.STETHOSCOPE:
                return this.processStethoscopeData(rawData);

            case DeviceType.THERMOMETER:
                return this.processThermometerData(rawData);

            case DeviceType.BLOOD_PRESSURE:
                return this.processBloodPressureData(rawData);

            case DeviceType.ECG:
                return this.processECGData(rawData);

            default:
                return rawData;
        }
    }

    /**
     * Process pulse oximeter data
     */
    private processPulseOximeterData(data: any): PulseOximeterData {
        // Enhanced null checks to prevent "Cannot read properties of undefined" errors
        if (!data) {
            console.warn('⚠️ Pulse oximeter data is null or undefined, using default values');
            return {
                spo2: 0,
                pulseRate: 0,
                timestamp: Date.now()
            };
        }

        const spo2 = this.safeParseInt(data.spo2, 0) || 0;
        const pulseRate = this.safeParseInt(data.pulse_rate, 0) || 0;
        const batteryLevel = data.battery_level ? this.safeParseInt(data.battery_level, undefined) : undefined;
        const signalQuality = data.signal_quality ? this.safeParseInt(data.signal_quality, undefined) : undefined;

        const result: PulseOximeterData = {
            spo2,
            pulseRate,
            timestamp: Date.now(),
            ...(batteryLevel !== undefined && { batteryLevel }),
            ...(signalQuality !== undefined && { signalQuality })
        };

        console.log('📊 Processed pulse oximeter data with null checks:', result);
        return result;
    }

    /**
     * Safely parse integer values with fallback
     */
    private safeParseInt(value: any, fallback: number | undefined): number | undefined {
        if (value === null || value === undefined || value === '') {
            return fallback;
        }

        const parsed = typeof value === 'string' ? parseInt(value, 10) : Number(value);
        return isNaN(parsed) ? fallback : parsed;
    }

    /**
     * Process stethoscope data
     */
    private processStethoscopeData(data: any): StethoscopeData {
        return {
            audioData: data.audio_data || new ArrayBuffer(0),
            duration: data.duration || 0,
            sampleRate: data.sample_rate || 44100,
            timestamp: Date.now(),
            heartRate: data.heart_rate ? parseInt(data.heart_rate) : undefined
        };
    }

    /**
     * Process thermometer data
     */
private processThermometerData(data: any): ThermometerData {
    return {
        temperature: parseFloat(data.temperature) || 0,
        unit: data.unit || 'celsius',
        timestamp: Date.now(),
        batteryLevel: data.battery_level ? parseInt(data.battery_level) : undefined,
        source: 'icare', // ✅
        sensorType: data?.sensor_type // ✅ no error after step 1
    };
}


    /**
     * Process blood pressure data
     */
    private processBloodPressureData(data: any): BloodPressureData {
        return {
            systolic: parseInt(data.systolic) || 0,
            diastolic: parseInt(data.diastolic) || 0,
            pulse: parseInt(data.pulse) || 0,
            timestamp: Date.now(),
            batteryLevel: data.battery_level ? parseInt(data.battery_level) : undefined
        };
    }

    /**
     * Process ECG data
     */
    private processECGData(data: any): ECGData {
        return {
            waveformData: data.waveform_data || [],
            heartRate: parseInt(data.heart_rate) || 0,
            duration: data.duration || 0,
            timestamp: Date.now(),
            leads: data.leads || []
        };
    }

    /**
     * Generate unique session ID
     */
    private generateSessionId(): string {
        return `android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
