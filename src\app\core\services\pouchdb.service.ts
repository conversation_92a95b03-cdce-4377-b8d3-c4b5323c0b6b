import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';
import { generateUUIDv4 } from '../../shared/utils';


@Injectable({ providedIn: 'root' })
export class PouchdbService {
  private db!: PouchDB.Database;
  private remoteDB!: PouchDB.Database; // Optional: For syncing with remote CouchDB server

  constructor() {
    PouchDB.plugin(PouchDBFind);
    // Initialize the database automatically with consultation data
    this.initDB('register_patient');
  
  }
  /**
   *  Initialize the database with a dynamic name
   * Example: this.initDB('users') OR this.initDB('products')
   */
  initDB(dbName: string) {
    this.db = new PouchDB(dbName, { adapter: 'idb' }); // IndexedDB adapter for browsers
    console.log(` PouchDB Initialized: ${dbName}`);
  }

   //  Add a new record
   
  addRecord(data: any): Observable<any> {
    data._id = generateUUIDv4(); // Generate a unique ID
    return from(this.db.put(data)).pipe(map(res => res), catchError(this.handleError));
  }

   //  Get all records
   
  getAllRecords<T>(): Observable<T[]> {
    return from(this.db.allDocs({ include_docs: true })).pipe(
      map(res => res.rows.map(r => r.doc as T)),
      catchError(this.handleError)
    );
  }

  // Get record by ID
  
  getRecordById<T>(id: string): Observable<T> {
    return from(this.db.get<T>(id)).pipe(map(res => res), catchError(this.handleError));
  }

   // Update record (must have _id & _rev)
  
  updateRecord(data: any): Observable<any> {
    if (!data._id || !data._rev) {
      return throwError(() => new Error(' Record must have _id and _rev to update'));
    }
    return from(this.db.put(data)).pipe(map(res => res), catchError(this.handleError));
  }

  //  Delete record (must have _id & _rev)
  
  deleteRecord(data: any): Observable<any> {
    if (!data._id || !data._rev) {
      return throwError(() => new Error(' Record must have _id and _rev to delete'));
    }
    return from(this.db.remove(data._id, data._rev)).pipe(map(res => res), catchError(this.handleError));
  }

  // 🔹================ MASTER DATA FUNCTIONS =================

  /**  Add/Update Master Data (Global static JSON) */
  addOrUpdateMasterData(data: any): Observable<any> {
    const docId = 'master_data';
    return from(
      this.db.get(docId).then(doc => {
        return this.db.put({ ...doc, ...data, _id: docId, _rev: doc._rev });
      }).catch(() => {
        return this.db.put({ _id: docId, ...data });
      })
    ).pipe(map(res => res), catchError(this.handleError));
  }

  /**  Fetch entire Master Data */
  getMasterData(): Observable<any> {
    return from(this.db.get('master_data')).pipe(map(res => res), catchError(this.handleError));
  }

  /**  Fetch specific table from Master Data */
  getMasterTable(tableName: string): Observable<any[]> {
    return from(this.db.get('master_data')).pipe(
      map((res: any) => res[tableName] || []),
      catchError(this.handleError)
    );
  }

  // 🔹================ CONSULTATION DATA FUNCTIONS =================

  /**  Get records by type (for consultation data like complaints, diagnosis, etc.) */
  getRecordsByType<T>(type: string): Observable<T[]> {
    return from(this.db.allDocs({ include_docs: true })).pipe(
      map(res => res.rows
        .map(r => r.doc as T)
        .filter((doc: any) => doc.type === type)
      ),
      catchError(this.handleError)
    );
  }

  //  Sync local DB with remote CouchDB/PouchDB server
  syncWithServer(): Observable<any> {
    return new Observable(observer => {
      if (!this.remoteDB) {
        observer.error(' Remote DB not configured!');
        return;
      }
      const syncHandler = this.db.sync(this.remoteDB, { live: true, retry: true })
        .on('change', info => observer.next(info))
        .on('paused', err => console.log(' Sync paused:', err))
        .on('active', () => console.log(' Sync active'))
        .on('error', err => observer.error(err));

      return () => syncHandler.cancel(); // Cleanup on unsubscribe
    });
  }

  //  Common Error Handler
  private handleError(error: any) {
    console.error(' PouchDB Error:', error);
    return throwError(() => error);
  }
}






// import { Injectable } from '@angular/core';
// import { Observable, from, throwError } from 'rxjs';
// import { catchError, map } from 'rxjs/operators';
// import PouchDB from 'pouchdb-browser';
// import PouchDBFind from 'pouchdb-find';
// import { mPatientData } from '../pages/patient-entry/patient-entry';
// import { generateUUIDv4 } from '../shared/utils';


// @Injectable({ providedIn: 'root' })
// export class PouchdbService {
//   private db!: PouchDB.Database;
//   private remoteDB!: PouchDB.Database;

//   constructor() {
//     PouchDB.plugin(PouchDBFind);
//     this.initDB('register_patient'); //  Change DB name if required
//   }

//   /**  Initialize Local PouchDB */
//   private initDB(name: string) {
//     this.db = new PouchDB(name, { adapter: 'idb' });
//     console.log(` PouchDB Initialized: ${name}`);
//   }

//   // 🔹================ PATIENT FUNCTIONS =================

//   addPatient(patient: mPatientData): Observable<any> {
//     patient._id = generateUUIDv4(); // Auto-generate unique ID
//     return from(this.db.put(patient)).pipe(map(res => res), catchError(this.handleError));
//   }

//   getAllPatients(): Observable<mPatientData[]> {
//     return from(this.db.allDocs({ include_docs: true })).pipe(
//       map(res => res.rows.map(r => r.doc as mPatientData)),
//       catchError(this.handleError)
//     );
//   }

//   getPatientById(id: string): Observable<mPatientData> {
//     return from(this.db.get<mPatientData>(id)).pipe(map(res => res), catchError(this.handleError));
//   }

//   updatePatient(patient: mPatientData): Observable<any> {
//     if (!patient._id || !patient._rev) {
//       return throwError(() => new Error(' Patient must have _id and _rev to update'));
//     }
//     return from(this.db.put(patient)).pipe(map(res => res), catchError(this.handleError));
//   }

//   deletePatient(patient: mPatientData): Observable<any> {
//     if (!patient._id || !patient._rev) {
//       return throwError(() => new Error(' Patient must have _id and _rev to delete'));
//     }
//     return from(this.db.remove(patient._id, patient._rev)).pipe(map(res => res), catchError(this.handleError));
//   }

//   // 🔹================ MASTER DATA FUNCTIONS =================

//   /**  Store or Update Entire Master Data JSON */
//   addOrUpdateMasterData(data: any): Observable<any> {
//     const docId = 'master_data';

//     return from(
//       this.db.get(docId).then(doc =>
//         this.db.put({ ...doc, ...data, _id: docId, _rev: doc._rev })
//       ).catch(() =>
//         this.db.put({ _id: docId, ...data })
//       )
//     ).pipe(map(res => res), catchError(this.handleError));
//   }

//   /**  Fetch Entire Master Data JSON */
//   getMasterData(): Observable<any> {
//     return from(this.db.get('master_data')).pipe(map(res => res), catchError(this.handleError));
//   }

//   /**  Fetch Specific Table (Example: countries, states, etc.) */
//   getMasterTable(tableName: string): Observable<any[]> {
//     return from(this.db.get('master_data')).pipe(
//       map((res: any) => res[tableName] || []),
//       catchError(this.handleError)
//     );
//   }

//   // 🔹================ SYNC FUNCTIONS =================

//   syncWithServer(): Observable<any> {
//     return new Observable((observer) => {
//       if (!this.remoteDB) {
//         observer.error(' Remote DB not configured!');
//         return;
//       }

//       const syncHandler = this.db.sync(this.remoteDB, { live: true, retry: true })
//         .on('change', info => observer.next(info))
//         .on('paused', err => console.log('⏸️ Sync paused:', err))
//         .on('active', () => console.log('▶️ Sync active'))
//         .on('error', err => {
//           console.error(' Sync error:', err);
//           observer.error(err);
//         });

//       return () => syncHandler.cancel();
//     });
//   }

//   // 🔹 Common Error Handler
//   private handleError(error: any) {
//     console.error(' PouchDB Error:', error);
//     return throwError(() => error);
//   }
// }







// import { Injectable } from '@angular/core';
// import { Observable, from, throwError } from 'rxjs';
// import { catchError, map } from 'rxjs/operators';
// import PouchDB from 'pouchdb-browser';
// import PouchDBFind from 'pouchdb-find';
// import { mPatientData } from '../pages/patient-entry/patient-entry';
// import { generateUUIDv4 } from '../shared/utils';

// @Injectable({ providedIn: 'root' })
// export class PouchdbService {
//   private db!: PouchDB.Database;
  
//    private remoteDB!: PouchDB.Database; 

//   constructor() {
//     PouchDB.plugin(PouchDBFind);
//     this.initDB('register_patient'); //  Keep same DB name
//   }

//   private initDB(name: string) {
//     this.db = new PouchDB(name, { adapter: 'idb' });
//     console.log(` PouchDB Initialized: ${name}`);
//   }

//   //  Add a new patient
//   addPatient(patient: mPatientData): Observable<any> {
//     patient._id = generateUUIDv4(); // Auto-generate ID
//     return from(this.db.put(patient)).pipe(
//       map((res) => res),
//       catchError(this.handleError)
//     );
//   }

//   //  Fetch all patients
//   getAllPatients(): Observable<mPatientData[]> {
//     return from(this.db.allDocs({ include_docs: true })).pipe(
//       map((res) => res.rows.map((r) => r.doc as mPatientData)),
//       catchError(this.handleError)
//     );
//   }

//   //  Get single patient by ID
//   getPatientById(id: string): Observable<mPatientData> {
//     return from(this.db.get<mPatientData>(id)).pipe(
//       map((res) => res),
//       catchError(this.handleError)
//     );
//   }

//   //  Update patient
//   updatePatient(patient: mPatientData): Observable<any> {
//     if (!patient._id || !patient._rev) {
//       return throwError(() => new Error(' Patient must have _id and _rev to update'));
//     }
//     return from(this.db.put(patient)).pipe(
//       map((res) => res),
//       catchError(this.handleError)
//     );
//   }

//   //  Delete patient
//   deletePatient(patient: mPatientData): Observable<any> {
//     if (!patient._id || !patient._rev) {
//       return throwError(() => new Error(' Patient must have _id and _rev to delete'));
//     }
//     return from(this.db.remove(patient._id, patient._rev)).pipe(
//       map((res) => res),
//       catchError(this.handleError)
//     );
//   }


  
//   // 🔹================ MASTER DATA FUNCTIONS =================

//   /** ✅ Store or update Master JSON data */
//   addOrUpdateMasterData(data: any): Observable<any> {
//     const docId = 'master_data';

//     return from(
//       this.db.get(docId).then(doc => {
//         return this.db.put({
//           ...doc,
//           ...data,
//           _id: docId,
//           _rev: doc._rev
//         });
//       }).catch(() => {
//         return this.db.put({
//           _id: docId,
//           ...data
//         });
//       })
//     ).pipe(map(res => res), catchError(this.handleError));
//   }

//   /** ✅ Fetch entire Master Data JSON */
//   getMasterData(): Observable<any> {
//     return from(this.db.get('master_data')).pipe(map(res => res), catchError(this.handleError));
//   }

//   /** ✅ Fetch specific table from Master Data (Example: country/state/district) */
//   getMasterTable(tableName: string): Observable<any[]> {
//     return from(this.db.get('master_data')).pipe(
//       map((res: any) => res[tableName] || []),
//       catchError(this.handleError)
//     );
//   }

//   // ========== SYNC FUNCTIONALITY ==========
//   // Sync local DB with remote CouchDB/PouchDB server
//   syncWithServer(): Observable<any> {
//     return new Observable((observer) => {
//       if (!this.remoteDB) {
//         observer.error(' Remote DB not configured!');
//         return;
//       }

//       // Setup continuous live sync (replicates changes both ways)
//       const syncHandler = this.db.sync(this.remoteDB, {
//         live: true,   // Keep syncing in real-time
//         retry: true,  // Retry if connection fails
//       })
//       .on('change', (info) => {
//         console.log('🔄 Sync change:', info);
//         observer.next(info);
//       })
//       .on('paused', (err) => {
//         console.log('⏸️ Sync paused:', err);
//       })
//       .on('active', () => {
//         console.log('▶️ Sync active');
//       })
//       .on('error', (err) => {
//         console.error(' Sync error:', err);
//         observer.error(err);
//       });

//       // Cleanup on unsubscribe
//       return () => syncHandler.cancel();
//     });
//   }


//   //  Common Error Handler
//   private handleError(error: any) {
//     console.error(' PouchDB Error:', error);
//     return throwError(() => error);
//   }
// }







// import { Injectable } from '@angular/core';

// import { mPatientData } from '../pages/patient-entry/patient-entry'; // 
// import { generateUUIDv4 } from '../shared/utils';  //
// import PouchDB from 'pouchdb-browser'; 

// @Injectable({
//   providedIn: 'root'
// })
// export class PouchdbService {

//   private db: PouchDB.Database;

//   patients: mPatientData[] = [];
//   newpatient: mPatientData =  new mPatientData() //{ _id:'',firstName: '', lastName: '', dateOfBirth: '', gender: '', phoneNumber: '', email: '', address: '', emergencyContactName: '', emergencyContactPhone: '', bloodGroup: ''}; 

//   // {_id = '',   _rev = '',   domainwisepid = '',   patientid = '',   first_name = '',   last_name = '',   date_of_birth = '',   age = '',   ageYears = '',   gender = '',   maritalstatus = '',   height = '',   weight = '',   mobile = '',   email = '',   head_of_household_fname = '',   head_of_household_lname = '',   country = '',   state = '',   district = '',   block = '',   village = '',   address = '',   projid = '',   head_of_household_mobile = '',   isAbhaPatient = '',   profile = '',   pastrecord = '',   createdat = '',   createdby = '',   domain = '',   uid = '',   prefix = '',   EhealthId = '',   MRN = '',   password = '',   consentformcheckstatus = '',   fingerPrintTemplate = '',   health_number = '',   health_address = '',   unique_id = '',   nationalId = '',   ethnicity = '',   subscriptionDetails = '',   localId = '',   patient_status = '',   patient_title = '',   postCode = '',   centerName = '',   status = '',   isSync = '', }
//   constructor() {
//     this.db = new PouchDB('register_patient');
//     // this.syncWithCouchDB();
//   }


//   async getAllPatient(): Promise<mPatientData[]> {
//     return this.db.allDocs({ include_docs: true }).then(res => res.rows.map(r => r.doc as mPatientData));
//   }

//   addPatient(patient: any) {
//     patient._id = generateUUIDv4()  ;
//     return this.db.put(patient);
//   }

//   updatePatient(patient: any) {
//     return this.db.put(patient);
//   }

//   deletePatient(patient: any) {
//     return this.db.remove(patient);
//   }

//   getPatient(id: string) {
//     return this.db.get(id);
//   }
// }
