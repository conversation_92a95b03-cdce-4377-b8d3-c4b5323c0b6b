import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,Inject } from '@angular/core';
import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { App } from '@capacitor/app';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { MedicalDeviceCommunicationPlugin } from 'src/app/core/plugins/medical-device-communication.plugin';
import { MedicalDeviceCommunicationService } from 'src/app/core/services/medical-device-communication.service';
import { DeviceDataManagerService } from 'src/app/core/services/device-data-manager.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  DeviceType,
  PulseOximeterData,
  ThermometerData,
  BloodPressureData,
  ECGData,
  StethoscopeData,
  DeviceCommunicationResult
} from 'src/app/core/interfaces/medical-device.interface';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-icare-spo2',
  templateUrl: './icare-spo2.page.html',
  styleUrls: ['./icare-spo2.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, DatePipe,MatTooltipModule]

})
export class IcareSpo2Page implements OnInit {
  resultdata: any;
  thermometerData: ThermometerData | null = null;
  bloodPressureData: BloodPressureData | null = null;
  ecgData: ECGData | null = null;
  stethoscopeData: StethoscopeData | null = null;
  isReading: boolean = true; // true initially so the button is shown


  // Available devices tracking
  availableDevices: DeviceType[] = [];
  isLoadingDevices = true;

  private deviceDataSubscription?: Subscription;
  private appStateSubscription?: any;
  private centralizedSubscriptions: Subscription[] = [];

  constructor(
    private ngZone: NgZone,
    private medicalDeviceService: MedicalDeviceCommunicationService,
    private deviceDataManager: DeviceDataManagerService,
         @Inject(MAT_DIALOG_DATA) public data: any,
  private dialogRef: MatDialogRef<any>
  ) { }

  async ngOnInit() {
    console.log('IcareDevicePage initialized');

    // Load available devices first
    await this.loadAvailableDevices();

    // Register listeners for device results
    this.registerResultListener();
    this.registerMedicalDeviceListener();
    this.registerCentralizedDeviceListeners();

    // Register app state listener to re-register listeners when app becomes active
    this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. isActive:', isActive);
      if (isActive) {
        console.log('App is active again → re-registering listeners');
        this.registerResultListener();
        this.registerMedicalDeviceListener();
        this.registerCentralizedDeviceListeners();
      }
    });
  }

  /**
   * Load available devices from the device manager
   */
  async loadAvailableDevices() {
    try {
      console.log('Loading available devices...');
      this.isLoadingDevices = true;

      this.availableDevices = await this.deviceDataManager.getAvailableDevices();

      console.log('Available devices loaded:', this.availableDevices);
      this.isLoadingDevices = false;
    } catch (error) {
      console.error('Error loading available devices:', error);
      this.isLoadingDevices = false;

      // Fallback to showing all supported devices
      this.availableDevices = [
        DeviceType.PULSE_OXIMETER,
      ];
    }
  }

  ngOnDestroy() {
    console.log('IcareDevicePage destroyed - cleaning up listeners');

    // Clean up subscriptions
    if (this.deviceDataSubscription) {
      this.deviceDataSubscription.unsubscribe();
    }

    // Clean up centralized subscriptions
    this.centralizedSubscriptions.forEach(sub => sub.unsubscribe());
    this.centralizedSubscriptions = [];

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }

  /**
   * Register centralized device data listeners
   */
  registerCentralizedDeviceListeners() {
    try {
      console.log('🔗 Registering centralized device data listeners');

      // Subscribe to pulse oximeter data
      const pulseOximeterSub = this.deviceDataManager.getDeviceData<PulseOximeterData>(DeviceType.PULSE_OXIMETER)
        .subscribe({
          next: (data) => {
            if (data) {
              this.ngZone.run(() => {
                this.resultdata = {
                  spo2: data.spo2 || 0,
                  pulse_rate: data.pulseRate || 0,
                  timestamp: data.timestamp || Date.now(),
                  batteryLevel: data.batteryLevel,
                  signalQuality: data.signalQuality,
                  source: 'CentralizedManager'
                };
                console.log('Pulse oximeter data from centralized manager:', this.resultdata);
                this.isReading = false;
              });
            }
          },
          error: (error) => console.error('Error in pulse oximeter subscription:', error)
        });

      // Store subscriptions for cleanup
      this.centralizedSubscriptions = [
        pulseOximeterSub,
      ];

      console.log('Centralized device listeners registered successfully');
    } catch (error) {
      console.error('Error registering centralized device listeners:', error);
    }
  }

  registerResultListener() {
    try {
      NovaIcareLauncher.addListener('PulseOximeterResult', (data: any) => {
        console.log('NovaIcareLauncher PulseOximeterResult event received:', data);

        this.ngZone.run(() => {
          // Comprehensive null checks to prevent "Cannot read properties of undefined" errors
          if (!data) {
            console.warn('Received null or undefined data from NovaIcareLauncher');
            this.resultdata = {
              spo2: 0,
              pulse_rate: 0,
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'No data received'
            };
            return;
          }

          // Check if data has the expected properties with safe access
          const hasSpo2 = data.hasOwnProperty('spo2') && data.spo2 !== null && data.spo2 !== undefined;
          const hasPulseRate = data.hasOwnProperty('pulse_rate') && data.pulse_rate !== null && data.pulse_rate !== undefined;

          if (hasSpo2 || hasPulseRate) {
            this.resultdata = {
              spo2: this.safeParseNumber(data.spo2, 0),
              pulse_rate: this.safeParseNumber(data.pulse_rate, 0),
              timestamp: Date.now(),
              source: 'NovaIcareLauncher'
            };
            console.log(' Result data updated from NovaIcareLauncher:', this.resultdata);
          } else {
            console.warn('Invalid data structure received from NovaIcareLauncher:', this.stringifyData(data));
            this.resultdata = {
              spo2: 0,
              pulse_rate: 0,
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'Invalid data structure'
            };
          }
        });
      });

      console.log('NovaIcareLauncher listener registered');
    } catch (error) {
      console.error('Error registering NovaIcareLauncher listener:', this.stringifyError(error));
    }
  }

  registerMedicalDeviceListener() {
    try {
      // Subscribe to medical device communication service
      this.deviceDataSubscription = this.medicalDeviceService.getDeviceDataObservable()
        .subscribe({
          next: (result) => {
            console.log('MedicalDeviceService result received:', result);

            if (result.success && result.deviceType === DeviceType.PULSE_OXIMETER && result.data) {
              this.ngZone.run(() => {
                const pulseOximeterData = result.data as PulseOximeterData;

                this.resultdata = {
                  spo2: pulseOximeterData.spo2 || 0,
                  pulse_rate: pulseOximeterData.pulseRate || 0,
                  timestamp: pulseOximeterData.timestamp || Date.now(),
                  batteryLevel: pulseOximeterData.batteryLevel,
                  signalQuality: pulseOximeterData.signalQuality,
                  source: 'MedicalDeviceService'
                };

                console.log(' Result data updated from MedicalDeviceService:', this.resultdata);
              });
            } else if (!result.success) {
              console.warn('Device result failed:', result.error);

              // Show user-friendly error message
              this.ngZone.run(() => {
                this.resultdata = {
                  error: result.error || 'Failed to get device result',
                  timestamp: Date.now(),
                  source: 'MedicalDeviceService'
                };
              });
            }
          },
          error: (error) => {
            console.error('Error in medical device subscription:', error);

            this.ngZone.run(() => {
              this.resultdata = {
                error: 'Connection error with medical device service',
                timestamp: Date.now(),
                source: 'MedicalDeviceService'
              };
            });
          }
        });

      console.log('MedicalDeviceService listener registered');
    } catch (error) {
      console.error('Error registering MedicalDeviceService listener:', error);
    }
  }

  /**
   * Safely parse numeric values with fallback
   */
  private safeParseNumber(value: any, fallback: number): number {
    if (value === null || value === undefined || value === '') {
      return fallback;
    }

    const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
    return isNaN(parsed) ? fallback : parsed;
  }

  /**
   * Safely stringify data for logging
   */
  private stringifyData(data: any): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * Safely stringify errors for logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }
    return String(error);
  }


  async launchPulseOximeter() {
    console.log('🚀 Launching Pulse Oximeter via MedicalDeviceCommunicationPlugin...');
    this.resultdata = null;
    this.isReading = true;
    try {
      const res = await NovaIcareLauncher.launchDeviceWithResult({
        deviceType: DeviceType.PULSE_OXIMETER,
        class_name: 'io.ionic.starter.MainActivity',
        package_name: 'io.ionic.starter',
        language: 'en',
        patient_id: '12345',
        real_id: '',
      });
      console.log('Legacy launch success:', res);

      if (!res.success) {
        console.warn('Legacy launch failed:', res.error);
        alert('Legacy launch failed: ' + (res.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('Legacy launch failed:', err);
      alert('Legacy launch failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  }

  onDialogClose() {
  this.dialogRef.close();
}

}
