import { Injectable } from '@angular/core';
import { WebSocketSubject, webSocket } from 'rxjs/webSocket';
import { BehaviorSubject, Subject, Observable, timer } from 'rxjs';
import { takeUntil, retry, catchError, filter } from 'rxjs/operators';
import {
    DeviceCommunicationProvider,
    DeviceLaunchOptions,
    DeviceCommunicationResult,
    DeviceStatus,
    DeviceType,
    PlatformType,
    PulseOximeterData,
    StethoscopeData,
    ThermometerData,
    BloodPressureData,
    ECGData
} from '../interfaces/medical-device.interface';
import { MedicalDeviceConfig } from '../config/medical-device.config';
import { PlatformDetectorService } from './platform-detector.service';
import { environment } from '../../../environments/environment';

/**
 * WebSocket connection interface
 */
interface WebSocketConnection {
    id: string;
    deviceType: DeviceType;
    socket$: WebSocketSubject<any>;
    destroy$: Subject<void>;
    isConnected: boolean;
    lastActivity: number;
    reconnectAttempts: number;
}

/**
 * WebSocket Device Communication Service
 * Enterprise-level service for WebSocket-based communication with Jetty server
 */
@Injectable({
    providedIn: 'root'
})
export class WebSocketDeviceService extends DeviceCommunicationProvider {

    readonly platform: PlatformType;
    readonly supportedDevices = [
        DeviceType.PULSE_OXIMETER,
        DeviceType.STETHOSCOPE,
        DeviceType.THERMOMETER,
        DeviceType.BLOOD_PRESSURE,
        DeviceType.ECG
    ];

    private connections = new Map<DeviceType, WebSocketConnection>();
    private reconnectAttempts = new Map<DeviceType, number>();
    private destroy$ = new Subject<void>();

    // Public observables for device data
    public deviceData$ = new BehaviorSubject<DeviceCommunicationResult | null>(null);
    public connectionStatus$ = new BehaviorSubject<Map<DeviceType, boolean>>(new Map());

    constructor(private platformDetector: PlatformDetectorService) {
        super();
        this.platform = this.platformDetector.currentPlatform;

        // Only initialize WebSocket service if not on Android
        if (this.platform === PlatformType.ANDROID) {
            console.log('📱 Skipping WebSocket Device Service initialization on Android platform');
            return;
        }

        console.log('🔌 WebSocket Device Service initialized for platform:', this.platform);
    }

    /**
     * Launch device using WebSocket communication
     */
    async launchDevice(options: DeviceLaunchOptions): Promise<DeviceCommunicationResult> {
        try {
            console.log('🚀 Launching WebSocket device:', options);

            // Validate device support
            if (!this.supportedDevices.includes(options.deviceType)) {
                throw new Error(`Device type ${options.deviceType} not supported via WebSocket`);
            }

            // Get or create WebSocket connection
            const connection = await this.getOrCreateConnection(options.deviceType);

            // Send device launch command
            const launchCommand = this.createLaunchCommand(options);
            connection.socket$.next(launchCommand);

            // Generate session ID for tracking
            const sessionId = this.generateSessionId();

            return {
                success: true,
                data: { sessionId, connectionId: connection.id },
                platform: this.platform,
                deviceType: options.deviceType,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ WebSocket device launch failed:', error);

            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                platform: this.platform,
                deviceType: options.deviceType,
                timestamp: Date.now()
            };
        }
    }

    /**
     * Get device status
     */
    async getDeviceStatus(deviceType: DeviceType): Promise<DeviceStatus> {
        const connection = this.connections.get(deviceType);
        const isConnected = connection?.isConnected || false;

        return {
            isConnected,
            lastCommunication: connection?.lastActivity,
            deviceInfo: {
                model: 'Jetty WebSocket Device',
                version: '1.0.0'
            }
        };
    }

    /**
     * Disconnect device
     */
    async disconnect(deviceType: DeviceType): Promise<void> {
        const connection = this.connections.get(deviceType);

        if (connection) {
            // Send disconnect command
            connection.socket$.next({
                type: MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.DEVICE_DISCONNECT,
                deviceType,
                timestamp: Date.now()
            });

            // Close connection
            connection.socket$.complete();
            connection.destroy$.next();
            connection.destroy$.complete();

            this.connections.delete(deviceType);
            this.updateConnectionStatus();

            console.log('🔌 Disconnected WebSocket device:', deviceType);
        }
    }

    /**
     * Get device data observable
     */
    getDeviceDataObservable(deviceType: DeviceType): Observable<DeviceCommunicationResult> {
        return this.deviceData$.asObservable().pipe(
            takeUntil(this.destroy$),
            // Filter out null values and ensure type safety
            filter((data): data is DeviceCommunicationResult => data !== null),
            catchError(error => {
                console.error('❌ Device data observable error:', error);
                throw error;
            })
        );
    }

    /**
     * Get connection status observable
     */
    getConnectionStatusObservable(): Observable<Map<DeviceType, boolean>> {
        return this.connectionStatus$.asObservable();
    }

    /**
     * Detect current platform
     */
    private detectPlatform(): PlatformType {
        if (typeof window !== 'undefined' && (window as any).electronAPI?.isElectron) {
            return PlatformType.ELECTRON;
        }
        return PlatformType.WEB;
    }

    /**
     * Get or create WebSocket connection for device
     */
    private async getOrCreateConnection(deviceType: DeviceType): Promise<WebSocketConnection> {
        let connection = this.connections.get(deviceType);

        if (!connection || !connection.isConnected) {
            connection = await this.createConnection(deviceType);
            this.connections.set(deviceType, connection);
        }

        return connection;
    }

    /**
     * Create new WebSocket connection
     */
    private async createConnection(deviceType: DeviceType): Promise<WebSocketConnection> {
        const wsUrl = this.getWebSocketUrl(deviceType);
        const connectionId = this.generateConnectionId();

        console.log('🔗 Creating WebSocket connection:', { deviceType, wsUrl, connectionId });

        const destroy$ = new Subject<void>();
        const socket$ = webSocket({
            url: wsUrl,
            deserializer: ({ data }) => {
                try {
                    return typeof data === 'string' ? JSON.parse(data) : data;
                } catch {
                    return data;
                }
            },
            serializer: (msg) => JSON.stringify(msg),
            openObserver: {
                next: () => {
                    console.log(`✅ WebSocket connected: ${deviceType} at ${wsUrl}`);
                    this.handleConnectionOpen(deviceType, connectionId);
                }
            },
            closeObserver: {
                next: () => {
                    console.log(`🔌 WebSocket disconnected: ${deviceType}`);
                    this.handleConnectionClose(deviceType, connectionId);
                }
            }
        });

        // Subscribe to messages
        socket$.pipe(
            takeUntil(destroy$),
            retry({
                count: MedicalDeviceConfig.TIMEOUTS.MAX_RETRY_ATTEMPTS,
                delay: MedicalDeviceConfig.TIMEOUTS.RETRY_DELAY
            }),
            catchError(error => {
                console.error(`❌ WebSocket error for ${deviceType}:`, error);
                this.handleConnectionError(deviceType, error);
                throw error;
            })
        ).subscribe({
            next: (message) => this.handleMessage(deviceType, message),
            error: (error) => this.handleConnectionError(deviceType, error)
        });

        const connection: WebSocketConnection = {
            id: connectionId,
            deviceType,
            socket$,
            destroy$,
            isConnected: false,
            lastActivity: Date.now(),
            reconnectAttempts: 0
        };

        // Setup heartbeat
        this.setupHeartbeat(connection);

        return connection;
    }

    /**
     * Get WebSocket URL for device type
     */
    private getWebSocketUrl(deviceType: DeviceType): string {
        const env = environment.production ? 'production' : 'development';
        return MedicalDeviceConfig.getWebSocketUrl(deviceType, env);
    }

    /**
     * Create device launch command
     */
    private createLaunchCommand(options: DeviceLaunchOptions): any {
        return {
            type: MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.DEVICE_CONNECT,
            deviceType: options.deviceType,
            patientId: options.patientId,
            realId: options.realId,
            language: options.language || 'en',
            sessionId: options.sessionId,
            timestamp: Date.now(),
            ...options.additionalParams
        };
    }

    /**
     * Handle WebSocket connection open
     */
    private handleConnectionOpen(deviceType: DeviceType, connectionId: string): void {
        const connection = this.connections.get(deviceType);
        if (connection && connection.id === connectionId) {
            connection.isConnected = true;
            connection.lastActivity = Date.now();
            connection.reconnectAttempts = 0;
            this.updateConnectionStatus();
        }
    }

    /**
     * Handle WebSocket connection close
     */
    private handleConnectionClose(deviceType: DeviceType, connectionId: string): void {
        const connection = this.connections.get(deviceType);
        if (connection && connection.id === connectionId) {
            connection.isConnected = false;
            this.updateConnectionStatus();

            // Attempt reconnection if not manually disconnected
            this.attemptReconnection(deviceType);
        }
    }

    /**
     * Handle WebSocket connection error
     */
    private handleConnectionError(deviceType: DeviceType, error: any): void {
        console.error(`❌ WebSocket connection error for ${deviceType}:`, error);

        this.deviceData$.next({
            success: false,
            error: error.message || 'WebSocket connection error',
            platform: this.platform,
            deviceType,
            timestamp: Date.now()
        });
    }

    /**
     * Handle incoming WebSocket message
     */
    private handleMessage(deviceType: DeviceType, message: any): void {
        try {
            console.log('📥 WebSocket message received:', { deviceType, message });

            const connection = this.connections.get(deviceType);
            if (connection) {
                connection.lastActivity = Date.now();
            }

            // Process message based on type
            switch (message.type) {
                case MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.DEVICE_DATA:
                    this.handleDeviceData(deviceType, message.data);
                    break;

                case MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.DEVICE_STATUS:
                    this.handleDeviceStatus(deviceType, message.data);
                    break;

                case MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.DEVICE_ERROR:
                    this.handleDeviceError(deviceType, message.data);
                    break;

                case MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.PONG:
                    // Heartbeat response - connection is alive
                    break;

                default:
                    // Handle legacy message format
                    this.handleLegacyMessage(deviceType, message);
            }
        } catch (error) {
            console.error('❌ Error handling WebSocket message:', error);
        }
    }

    /**
     * Handle device data message
     */
    private handleDeviceData(deviceType: DeviceType, data: any): void {
        const processedData = this.processDeviceData(deviceType, data);

        this.deviceData$.next({
            success: true,
            data: processedData,
            platform: this.platform,
            deviceType,
            timestamp: Date.now()
        });
    }

    /**
     * Handle device status message
     */
    private handleDeviceStatus(deviceType: DeviceType, data: any): void {
        console.log('📊 Device status update:', { deviceType, data });
        // Could emit status updates if needed
    }

    /**
     * Handle device error message
     */
    private handleDeviceError(deviceType: DeviceType, data: any): void {
        this.deviceData$.next({
            success: false,
            error: data.message || 'Device error',
            platform: this.platform,
            deviceType,
            timestamp: Date.now()
        });
    }

    /**
     * Handle legacy message format (for backward compatibility)
     */
    private handleLegacyMessage(deviceType: DeviceType, message: any): void {
        // Handle existing WebSocket message formats
        if (typeof message === 'string') {
            if (message.includes('plotSpo2Graph')) {
                const data = message.split('~')[1];
                this.handleDeviceData(DeviceType.PULSE_OXIMETER, { plotData: data });
            } else if (message.includes('setCurrentSpo2')) {
                const spo2Value = message.split('~')[1];
                this.handleDeviceData(DeviceType.PULSE_OXIMETER, { spo2: spo2Value });
            } else if (message.includes('changeBatteryStatus')) {
                const batteryLevel = message.split('~')[1];
                this.handleDeviceData(DeviceType.PULSE_OXIMETER, { batteryLevel });
            }
        }
    }

    /**
     * Process device-specific data
     */
    private processDeviceData(deviceType: DeviceType, rawData: any): any {
        switch (deviceType) {
            case DeviceType.PULSE_OXIMETER:
                return this.processPulseOximeterData(rawData);
            case DeviceType.STETHOSCOPE:
                return this.processStethoscopeData(rawData);
            case DeviceType.THERMOMETER:
                return this.processThermometerData(rawData);
            case DeviceType.BLOOD_PRESSURE:
                return this.processBloodPressureData(rawData);
            case DeviceType.ECG:
                return this.processECGData(rawData);
            default:
                return rawData;
        }
    }

    /**
     * Process pulse oximeter data
     */
    private processPulseOximeterData(data: any): PulseOximeterData {
        // Enhanced null checks to prevent "Cannot read properties of undefined" errors
        if (!data) {
            console.warn('⚠️ WebSocket pulse oximeter data is null or undefined, using default values');
            return {
                spo2: 0,
                pulseRate: 0,
                timestamp: Date.now()
            };
        }

        const spo2 = this.safeParseInt(data.spo2, 0);
        const pulseRate = this.safeParseInt(data.pulse_rate || data.pulseRate, 0);
        const batteryLevel = data.battery_level ? this.safeParseInt(data.battery_level, undefined) : undefined;
        const signalQuality = data.signal_quality ? this.safeParseInt(data.signal_quality, undefined) : undefined;

        const result: PulseOximeterData = {
            spo2: spo2 || 0,
            pulseRate: pulseRate || 0,
            timestamp: Date.now(),
            ...(batteryLevel !== undefined && { batteryLevel }),
            ...(signalQuality !== undefined && { signalQuality })
        };

        console.log('📊 WebSocket processed pulse oximeter data with null checks:', result);
        return result;
    }

    /**
     * Safely parse integer values with fallback
     */
    private safeParseInt(value: any, fallback: number | undefined): number | undefined {
        if (value === null || value === undefined || value === '') {
            return fallback;
        }

        const parsed = typeof value === 'string' ? parseInt(value, 10) : Number(value);
        return isNaN(parsed) ? fallback : parsed;
    }

    /**
     * Process stethoscope data
     */
    private processStethoscopeData(data: any): StethoscopeData {
        if (!data) {
            console.warn('⚠️ WebSocket stethoscope data is null or undefined, using default values');
            return {
                audioData: new ArrayBuffer(0),
                duration: 0,
                sampleRate: 44100,
                timestamp: Date.now()
            };
        }

        const duration = this.safeParseInt(data.duration, 0) || 0;
        const sampleRate = this.safeParseInt(data.sample_rate, 44100) || 44100;
        const heartRate = this.safeParseInt(data.heart_rate, undefined);

        return {
            audioData: data.audio_data || new ArrayBuffer(0),
            duration,
            sampleRate,
            timestamp: Date.now(),
            ...(heartRate !== undefined && { heartRate })
        };
    }

    /**
     * Process thermometer data
     */
private processThermometerData(data: any): ThermometerData {
    if (!data) {
        console.warn('⚠️ WebSocket thermometer data is null or undefined, using default values');
        return {
            temperature: 0,
            unit: 'celsius',
            timestamp: Date.now(),
            source: 'icare',
            sensorType: undefined
        };
    }

    const temperature = this.safeParseFloat(data.temperature, 0) ?? 0;
    const batteryLevel = this.safeParseInt(data.battery_level, undefined);

    return {
        temperature: Math.round(temperature * 10) / 10,
        unit: (data.unit === 'fahrenheit' ? 'fahrenheit' : 'celsius'), // safety check
        timestamp: Date.now(),
        ...(batteryLevel !== undefined ? { batteryLevel } : {}),
        source: 'AndroidIntentService',
        sensorType: data.sensor_type
    };
}



    /**
     * Process blood pressure data
     */
    private processBloodPressureData(data: any): BloodPressureData {
        if (!data) {
            console.warn('⚠️ WebSocket blood pressure data is null or undefined, using default values');
            return {
                systolic: 0,
                diastolic: 0,
                pulse: 0,
                timestamp: Date.now()
            };
        }

        const systolic = this.safeParseInt(data.systolic, 0) || 0;
        const diastolic = this.safeParseInt(data.diastolic, 0) || 0;
        const pulse = this.safeParseInt(data.pulse, 0) || 0;
        const batteryLevel = this.safeParseInt(data.battery_level, undefined);

        return {
            systolic,
            diastolic,
            pulse,
            timestamp: Date.now(),
            ...(batteryLevel !== undefined && { batteryLevel })
        };
    }

    /**
     * Process ECG data
     */
    private processECGData(data: any): ECGData {
        if (!data) {
            console.warn('⚠️ WebSocket ECG data is null or undefined, using default values');
            return {
                waveformData: [],
                heartRate: 0,
                duration: 0,
                timestamp: Date.now(),
                leads: []
            };
        }

        const heartRate = this.safeParseInt(data.heart_rate, 0) || 0;
        const duration = this.safeParseInt(data.duration, 0) || 0;

        return {
            waveformData: Array.isArray(data.waveform_data) ? data.waveform_data : [],
            heartRate,
            duration,
            timestamp: Date.now(),
            leads: Array.isArray(data.leads) ? data.leads : []
        };
    }

    /**
     * Safely parse float values with fallback
     */
    private safeParseFloat(value: any, fallback: number | undefined): number | undefined {
        if (value === null || value === undefined || value === '') {
            return fallback;
        }

        const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
        return isNaN(parsed) ? fallback : parsed;
    }

    /**
     * Setup heartbeat for connection
     */
    private setupHeartbeat(connection: WebSocketConnection): void {
        timer(0, MedicalDeviceConfig.TIMEOUTS.WEBSOCKET_PING_INTERVAL)
            .pipe(takeUntil(connection.destroy$))
            .subscribe(() => {
                if (connection.isConnected) {
                    connection.socket$.next({
                        type: MedicalDeviceConfig.WEBSOCKET_MESSAGE_TYPES.PING,
                        timestamp: Date.now()
                    });
                }
            });
    }

    /**
     * Attempt reconnection
     */
    private attemptReconnection(deviceType: DeviceType): void {
        const currentAttempts = this.reconnectAttempts.get(deviceType) || 0;
        const maxAttempts = MedicalDeviceConfig.TIMEOUTS.MAX_RETRY_ATTEMPTS;

        if (currentAttempts < maxAttempts) {
            this.reconnectAttempts.set(deviceType, currentAttempts + 1);

            setTimeout(async () => {
                try {
                    console.log(`🔄 Attempting reconnection for ${deviceType} (${currentAttempts + 1}/${maxAttempts})`);
                    await this.getOrCreateConnection(deviceType);
                } catch (error) {
                    console.error(`❌ Reconnection failed for ${deviceType}:`, error);
                }
            }, MedicalDeviceConfig.TIMEOUTS.RETRY_DELAY);
        } else {
            console.error(`❌ Max reconnection attempts reached for ${deviceType}`);
            this.reconnectAttempts.delete(deviceType);
        }
    }

    /**
     * Update connection status
     */
    private updateConnectionStatus(): void {
        const statusMap = new Map<DeviceType, boolean>();

        this.connections.forEach((connection, deviceType) => {
            statusMap.set(deviceType, connection.isConnected);
        });

        this.connectionStatus$.next(statusMap);
    }

    /**
     * Generate unique session ID
     */
    private generateSessionId(): string {
        return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate unique connection ID
     */
    private generateConnectionId(): string {
        return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Cleanup on service destroy
     */
    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();

        // Close all connections
        this.connections.forEach((connection) => {
            connection.socket$.complete();
            connection.destroy$.next();
            connection.destroy$.complete();
        });

        this.connections.clear();
    }
}
