import { Compo<PERSON>, OnInit,NgZone, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { App } from '@capacitor/app';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { MedicalDeviceCommunicationService } from 'src/app/core/services/medical-device-communication.service';
import { Subscription } from 'rxjs';
import { DeviceType } from 'src/app/core/interfaces/medical-device.interface';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { inject } from '@angular/core';


@Component({
  selector: 'app-ecg-icare-device',
  templateUrl: './ecg-icare-device.page.html',
  styleUrls: ['./ecg-icare-device.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule,MatDialogModule]
})
export class EcgIcareDevicePage implements OnInit,OnDestroy  {

   resultdata: any;
  private deviceDataSubscription?: Subscription;
  private appStateSubscription?: any;
  dialogRef = inject(MatDialogRef<EcgIcareDevicePage>);

  constructor(
    private ngZone: NgZone,
    private medicalDeviceService: MedicalDeviceCommunicationService
  ) { }


  ngOnInit() {
    console.log('IcareDevicePage initialized');

    // Register listeners for device results
    this.registerECGMedicalDeviceListener();
    // Register app state listener to re-register listeners when app becomes active
    this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. isActive:', isActive);
      if (isActive) {
        console.log('App is active again → re-registering listeners');
        this.registerECGMedicalDeviceListener();
      }
    });
  }

  ngOnDestroy() {
    console.log('IcareDevicePage destroyed - cleaning up listeners');

    // Clean up subscriptions
    if (this.deviceDataSubscription) {
      this.deviceDataSubscription.unsubscribe();
    }

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }
// 🔹 NEW: launching via NovaIcareLauncher plugin
  async launchIcarePluginDevice() {
    console.log('🚀 Launching iCare Plugin...');
    try {
      const result = await NovaIcareLauncher.launchDeviceWithResult({
        deviceType: DeviceType.ECG,
        class_name: 'io.ionic.starter.MainActivity',
        package_name: 'io.ionic.starter',
        language: 'en',
        patient_id: '12345',
        real_id: '',
      });

      console.log('✅ Plugin launch result:', result);
    } catch (err) {
      console.error('❌ Plugin ');
    }
  }


  /**
   * Safely parse numeric values with fallback
   */
  private safeParseNumber(value: any, fallback: number): number {
    if (value === null || value === undefined || value === '') {
      return fallback;
    }

    const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
    return isNaN(parsed) ? fallback : parsed;
  }

  /**
   * Safely stringify data for logging
   */
  private stringifyData(data: any): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * Safely stringify errors for logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }
    return String(error);
  }

  

  
 registerECGMedicalDeviceListener() {
  try {
    NovaIcareLauncher.addListener('ECGResult', (data: any) => {
      console.log('📥 ECG Result received from NovaIcareLauncher:', data);

      this.ngZone.run(() => {
        this.resultdata = {
          pulse_rate: data?.pulse_rate,
          ecg_file_name: data?.file_name
        };
            console.log('✅ Pulse Rate:', this.resultdata.pulse_rate);
           console.log('✅ ECG File Name:', this.resultdata.ecg_file_name);

      });
    });     
  } catch (error) {
    console.error('Error registering ECG medical device listener:', error);
  }
 }

closeEcgSection() {
  this.dialogRef.close();
}

}
