<div class="pdp_title register-pt-heading margin-btm-10">
    <div style="text-align: center;">StethoScope
        <button class="material-toolbar-right" (click)="onDialogClose()">
            <i class="fa fa-close"></i></button></div>
    <div class="battery" *ngIf="battery">
        <div class="flexCol">
            <div class="battery-main" style="margin-top: -21px;">
                <div class="batteryContainer">
                    <div class="batteryOuter">
                        <div id="batteryLevel" #batteryLevel [ngStyle]="{'width.%': battery, 'background-color': batteryColor}" matTooltip="{{battery}}%">
                        </div>
                    </div>
                    <div class="batteryBump"></div>
                </div>
            </div>
        </div>
    </div>



</div>
<div>
    <p style="text-align: center;
    font-size: 13px;
    font-weight: bold;">steho_volume_control</p>


</div>
<div style="position: relative;text-align: center;margin-top: 10px;">
    <!-- <input id="volSlider" style="width:100px;" type="range" [ngModel]="volumeslider" min="0" max="12" value="0" step="2" (input)="stethoVolumeControl($event.target.value)" /> -->
</div>

<div style="position: relative;text-align: center;">
    <!-- <span>{{volumeslider}}</span> -->
</div>
<div id="stethoScopeGraph" style="height:300px;width:auto;"></div>
<!-- </div> -->