import { Component, OnInit, OnD<PERSON>roy, NgZone } from '@angular/core';
import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { App } from '@capacitor/app';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { MedicalDeviceCommunicationPlugin } from 'src/app/core/plugins/medical-device-communication.plugin';
import { MedicalDeviceCommunicationService } from 'src/app/core/services/medical-device-communication.service';
import { DeviceDataManagerService } from 'src/app/core/services/device-data-manager.service';
import {
  DeviceType,
  PulseOximeterData,
  ThermometerData,
  BloodPressureData,
  ECGData,
  StethoscopeData,
  DeviceCommunicationResult
} from 'src/app/core/interfaces/medical-device.interface';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-icare-device',
  templateUrl: './icare-device.page.html',
  styleUrls: ['./icare-device.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, DatePipe]
})
export class IcareDevicePage implements OnInit, OnDestroy {
  resultdata: any;
  thermometerData: ThermometerData | null = null;
  bloodPressureData: BloodPressureData | null = null;
  ecgData: ECGData | null = null;
  stethoscopeData: StethoscopeData | null = null;

  // Available devices tracking
  availableDevices: DeviceType[] = [];
  isLoadingDevices = true;

  private deviceDataSubscription?: Subscription;
  private appStateSubscription?: any;
  private centralizedSubscriptions: Subscription[] = [];

  constructor(
    private ngZone: NgZone,
    private medicalDeviceService: MedicalDeviceCommunicationService,
    private deviceDataManager: DeviceDataManagerService
  ) { }

  async ngOnInit() {
    console.log('IcareDevicePage initialized');

    // Load available devices first
    await this.loadAvailableDevices();

    // Register listeners for device results
    this.registerResultListener();
    this.registerMedicalDeviceListener();
    this.registerCentralizedDeviceListeners();

    // Register app state listener to re-register listeners when app becomes active
    this.appStateSubscription = App.addListener('appStateChange', ({ isActive }) => {
      console.log('App state changed. isActive:', isActive);
      if (isActive) {
        console.log('App is active again → re-registering listeners');
        this.registerResultListener();
        this.registerMedicalDeviceListener();
        this.registerCentralizedDeviceListeners();
      }
    });
  }

  /**
   * Load available devices from the device manager
   */
  async loadAvailableDevices() {
    try {
      console.log('🔍 Loading available devices...');
      this.isLoadingDevices = true;

      this.availableDevices = await this.deviceDataManager.getAvailableDevices();

      console.log('✅ Available devices loaded:', this.availableDevices);
      this.isLoadingDevices = false;
    } catch (error) {
      console.error('❌ Error loading available devices:', error);
      this.isLoadingDevices = false;

      // Fallback to showing all supported devices
      this.availableDevices = [
        DeviceType.PULSE_OXIMETER,
        DeviceType.STETHOSCOPE
      ];
    }
  }

  ngOnDestroy() {
    console.log('IcareDevicePage destroyed - cleaning up listeners');

    // Clean up subscriptions
    if (this.deviceDataSubscription) {
      this.deviceDataSubscription.unsubscribe();
    }

    // Clean up centralized subscriptions
    this.centralizedSubscriptions.forEach(sub => sub.unsubscribe());
    this.centralizedSubscriptions = [];

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }

  /**
   * Register centralized device data listeners
   */
  registerCentralizedDeviceListeners() {
    try {
      console.log('🔗 Registering centralized device data listeners');

      // Subscribe to pulse oximeter data
      const pulseOximeterSub = this.deviceDataManager.getDeviceData<PulseOximeterData>(DeviceType.PULSE_OXIMETER)
        .subscribe({
          next: (data) => {
            if (data) {
              this.ngZone.run(() => {
                this.resultdata = {
                  spo2: data.spo2 || 0,
                  pulse_rate: data.pulseRate || 0,
                  timestamp: data.timestamp || Date.now(),
                  batteryLevel: data.batteryLevel,
                  signalQuality: data.signalQuality,
                  source: 'CentralizedManager'
                };
                console.log('✅ Pulse oximeter data from centralized manager:', this.resultdata);
              });
            }
          },
          error: (error) => console.error('❌ Error in pulse oximeter subscription:', error)
        });

      // Subscribe to thermometer data
      const thermometerSub = this.deviceDataManager.getDeviceData<ThermometerData>(DeviceType.THERMOMETER)
        .subscribe({
          next: (data) => {
            this.ngZone.run(() => {
              this.thermometerData = data;
              console.log('🌡️ Thermometer data updated:', data);
            });
          },
          error: (error) => console.error('❌ Error in thermometer subscription:', error)
        });

      // Subscribe to blood pressure data
      const bloodPressureSub = this.deviceDataManager.getDeviceData<BloodPressureData>(DeviceType.BLOOD_PRESSURE)
        .subscribe({
          next: (data) => {
            this.ngZone.run(() => {
              this.bloodPressureData = data;
              console.log('🩺 Blood pressure data updated:', data);
            });
          },
          error: (error) => console.error('❌ Error in blood pressure subscription:', error)
        });

      // Subscribe to ECG data
      const ecgSub = this.deviceDataManager.getDeviceData<ECGData>(DeviceType.ECG)
        .subscribe({
          next: (data) => {
            this.ngZone.run(() => {
              this.ecgData = data;
              console.log('📈 ECG data updated:', data);
            });
          },
          error: (error) => console.error('❌ Error in ECG subscription:', error)
        });

      // Subscribe to stethoscope data
      const stethoscopeSub = this.deviceDataManager.getDeviceData<StethoscopeData>(DeviceType.STETHOSCOPE)
        .subscribe({
          next: (data) => {
            this.ngZone.run(() => {
              this.stethoscopeData = data;
              console.log('🔊 Stethoscope data updated:', data);
            });
          },
          error: (error) => console.error('❌ Error in stethoscope subscription:', error)
        });

      // Store subscriptions for cleanup
      this.centralizedSubscriptions = [
        pulseOximeterSub,
        thermometerSub,
        bloodPressureSub,
        ecgSub,
        stethoscopeSub
      ];

      console.log('✅ Centralized device listeners registered successfully');
    } catch (error) {
      console.error('❌ Error registering centralized device listeners:', error);
    }
  }

  registerResultListener() {
    try {
      NovaIcareLauncher.addListener('PulseOximeterResult', (data: any) => {
        console.log('📥 NovaIcareLauncher PulseOximeterResult event received:', data);

        this.ngZone.run(() => {
          // Comprehensive null checks to prevent "Cannot read properties of undefined" errors
          if (!data) {
            console.warn('⚠️ Received null or undefined data from NovaIcareLauncher');
            this.resultdata = {
              spo2: 0,
              pulse_rate: 0,
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'No data received'
            };
            return;
          }

          // Check if data has the expected properties with safe access
          const hasSpo2 = data.hasOwnProperty('spo2') && data.spo2 !== null && data.spo2 !== undefined;
          const hasPulseRate = data.hasOwnProperty('pulse_rate') && data.pulse_rate !== null && data.pulse_rate !== undefined;

          if (hasSpo2 || hasPulseRate) {
            this.resultdata = {
              spo2: this.safeParseNumber(data.spo2, 0),
              pulse_rate: this.safeParseNumber(data.pulse_rate, 0),
              timestamp: Date.now(),
              source: 'NovaIcareLauncher'
            };
            console.log('✅ Result data updated from NovaIcareLauncher:', this.resultdata);
          } else {
            console.warn('⚠️ Invalid data structure received from NovaIcareLauncher:', this.stringifyData(data));
            this.resultdata = {
              spo2: 0,
              pulse_rate: 0,
              timestamp: Date.now(),
              source: 'NovaIcareLauncher',
              error: 'Invalid data structure'
            };
          }
        });
      });

      console.log('✅ NovaIcareLauncher listener registered');
    } catch (error) {
      console.error('❌ Error registering NovaIcareLauncher listener:', this.stringifyError(error));
    }
  }

  registerMedicalDeviceListener() {
    try {
      // Subscribe to medical device communication service
      this.deviceDataSubscription = this.medicalDeviceService.getDeviceDataObservable()
        .subscribe({
          next: (result) => {
            console.log('📥 MedicalDeviceService result received:', result);

            if (result.success && result.deviceType === DeviceType.PULSE_OXIMETER && result.data) {
              this.ngZone.run(() => {
                const pulseOximeterData = result.data as PulseOximeterData;

                this.resultdata = {
                  spo2: pulseOximeterData.spo2 || 0,
                  pulse_rate: pulseOximeterData.pulseRate || 0,
                  timestamp: pulseOximeterData.timestamp || Date.now(),
                  batteryLevel: pulseOximeterData.batteryLevel,
                  signalQuality: pulseOximeterData.signalQuality,
                  source: 'MedicalDeviceService'
                };

                console.log('✅ Result data updated from MedicalDeviceService:', this.resultdata);
              });
            } else if (!result.success) {
              console.warn('⚠️ Device result failed:', result.error);

              // Show user-friendly error message
              this.ngZone.run(() => {
                this.resultdata = {
                  error: result.error || 'Failed to get device result',
                  timestamp: Date.now(),
                  source: 'MedicalDeviceService'
                };
              });
            }
          },
          error: (error) => {
            console.error('❌ Error in medical device subscription:', error);

            this.ngZone.run(() => {
              this.resultdata = {
                error: 'Connection error with medical device service',
                timestamp: Date.now(),
                source: 'MedicalDeviceService'
              };
            });
          }
        });

      console.log('✅ MedicalDeviceService listener registered');
    } catch (error) {
      console.error('❌ Error registering MedicalDeviceService listener:', error);
    }
  }

  /**
   * Safely parse numeric values with fallback
   */
  private safeParseNumber(value: any, fallback: number): number {
    if (value === null || value === undefined || value === '') {
      return fallback;
    }

    const parsed = typeof value === 'string' ? parseFloat(value) : Number(value);
    return isNaN(parsed) ? fallback : parsed;
  }

  /**
   * Safely stringify data for logging
   */
  private stringifyData(data: any): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * Safely stringify errors for logging
   */
  private stringifyError(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      try {
        return JSON.stringify(error);
      } catch {
        return String(error);
      }
    }
    return String(error);
  }

  async launchStethoscope() {
    console.log('🚀 Launching Stethoscope via MedicalDeviceCommunicationPlugin...');
    try {
      const res = await MedicalDeviceCommunicationPlugin.launchDevice({
        deviceType: DeviceType.STETHOSCOPE,
        packageName: 'io.ionic.starter',
        className: 'io.ionic.starter.MainActivity',
        patientId: '12345',
        language: 'en'
      });
      console.log('✅ Stethoscope launch success:', res);

      if (!res.success) {
        console.warn('⚠️ Stethoscope launch failed:', res.error);
        alert('Stethoscope launch failed: ' + (res.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('❌ Stethoscope launch failed:', err);
      alert('Stethoscope launch failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use launchStethoscope() instead
   */
  async launchStethoscopeLegacy() {
    console.log('🚀 Launching iCare via NovaIcareLauncher (Legacy)...');
    try {
      // const res = await NovaIcareLauncher.launchStethoWithResult({
      //   class_name: 'io.ionic.starter.MainActivity',
      //   package_name: 'io.ionic.starter',
      //   language: 'en',
      //   patient_id: '12345',
      //   real_id: '',
      // });
      // console.log('✅ Legacy launch success:', res);

      // if (!res.success) {
      //   console.warn('⚠️ Legacy launch failed:', res.error);
      //   alert('Legacy launch failed: ' + (res.error || 'Unknown error'));
      // }
    } catch (err) {
      console.error('❌ Legacy launch failed:', err);
      alert('Legacy launch failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  }

  async launchPulseOximeter() {
    // console.log('🚀 Launching Pulse Oximeter via MedicalDeviceCommunicationPlugin...');
    //   try {
    //   const res = await NovaIcareLauncher.launchStethoWithResult({
    //     deviceType: DeviceType.PULSE_OXIMETER,
    //     class_name: 'io.ionic.starter.MainActivity',
    //     package_name: 'io.ionic.starter',
    //     language: 'en',
    //     patient_id: '12345',
    //     real_id: '',
    //   });
    //   console.log('✅ Legacy launch success:', res);

    //   if (!res.success) {
    //     console.warn('⚠️ Legacy launch failed:', res.error);
    //     alert('Legacy launch failed: ' + (res.error || 'Unknown error'));
    //   }
    // } catch (err) {
    //   console.error('❌ Legacy launch failed:', err);
    //   alert('Legacy launch failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    // }
  }
  

  /**
   * Launch via centralized manager (fallback)
   */
  async launchPulseOximeterViaCentralized() {
    console.log('🚀 Launching Pulse Oximeter via Centralized Manager...');
    try {
      const result = await this.deviceDataManager.launchDevice(
        DeviceType.PULSE_OXIMETER,
        '12345',
        { language: 'en' }
      );

      console.log('✅ Pulse oximeter launch result:', result);

      if (!result.success) {
        console.error('❌ Failed to launch pulse oximeter:', result.error);
        alert('Failed to launch pulse oximeter: ' + result.error);
      }
    } catch (error) {
      console.error('❌ Error launching pulse oximeter:', error);
      alert('Error launching pulse oximeter: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

   async launchThermometer() {
    console.log('🚀 Launching Thermometer via MedicalDeviceCommunicationPlugin...');
  //   this.resultdata = null;
  // this.isReading = true;
    //   try {
    //   const res = await NovaIcareLauncher.launchTempWithResult({
    //     deviceType: DeviceType.THERMOMETER,
    //     class_name: 'io.ionic.starter.MainActivity',
    //     package_name: 'io.ionic.starter',
    //     language: 'en',
    //     patient_id: '12345',
    //     real_id: '',
    //   });
    //   console.log('Legacy launch success:', res);

    //   if (!res.success) {
    //     console.warn('Legacy launch failed:', res.error);
    //     alert('Legacy launch failed: ' + (res.error || 'Unknown error'));
    //   }
    // } catch (err) {
    //   console.error('Legacy launch failed:', err);
    //   alert('Legacy launch failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
    // }
  }
  

  async launchThermometerold() {
    console.log('🌡️ Launching Thermometer via MedicalDeviceCommunicationPlugin...');
    try {
      const result = await MedicalDeviceCommunicationPlugin.launchDevice({
        deviceType: DeviceType.THERMOMETER,
        packageName: 'io.ionic.starter',
        className: 'io.ionic.starter.MainActivity',
        patientId: '12345',
        language: 'en'
      });

      console.log('✅ Thermometer launch result:', result);

      if (!result.success) {
        console.error('❌ Failed to launch thermometer:', result.error);
        alert('Failed to launch thermometer: ' + result.error);
      }
    } catch (error) {
      console.error('❌ Error launching thermometer:', error);
      alert('Error launching thermometer: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  async launchBloodPressure() {
    console.log('🩺 Launching Blood Pressure Monitor via MedicalDeviceCommunicationPlugin...');
    try {
      const result = await MedicalDeviceCommunicationPlugin.launchDevice({
        deviceType: DeviceType.BLOOD_PRESSURE,
        packageName: 'io.ionic.starter',
        className: 'io.ionic.starter.MainActivity',
        patientId: '12345',
        language: 'en'
      });

      console.log('✅ Blood pressure monitor launch result:', result);

      if (!result.success) {
        console.error('❌ Failed to launch blood pressure monitor:', result.error);
        alert('Failed to launch blood pressure monitor: ' + result.error);
      }
    } catch (error) {
      console.error('❌ Error launching blood pressure monitor:', error);
      alert('Error launching blood pressure monitor: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  async launchECG() {
    console.log('📈 Launching ECG via MedicalDeviceCommunicationPlugin...');
    try {
      const result = await MedicalDeviceCommunicationPlugin.launchDevice({
        deviceType: DeviceType.ECG,
        packageName: 'io.ionic.starter',
        className: 'io.ionic.starter.MainActivity',
        patientId: '12345',
        language: 'en'
      });

      console.log('✅ ECG launch result:', result);

      if (!result.success) {
        console.error('❌ Failed to launch ECG:', result.error);
        alert('Failed to launch ECG: ' + result.error);
      }
    } catch (error) {
      console.error('❌ Error launching ECG:', error);
      alert('Error launching ECG: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  /**
   * Launch via centralized manager (fallback method)
   */
  async launchStethoscopeViaCentralized() {
    console.log('🔊 Launching Stethoscope via Centralized Manager...');
    try {
      const result = await this.deviceDataManager.launchDevice(
        DeviceType.STETHOSCOPE,
        '12345',
        { language: 'en' }
      );

      console.log('✅ Stethoscope launch result:', result);

      if (!result.success) {
        console.error('❌ Failed to launch stethoscope:', result.error);
        alert('Failed to launch stethoscope: ' + result.error);
      }
    } catch (error) {
      console.error('❌ Error launching stethoscope:', error);
      alert('Error launching stethoscope: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  }

  /**
   * Clear data for specific device type
   */
  clearDeviceData(deviceType: DeviceType) {
    console.log(`🧹 Clearing data for ${deviceType}`);
    this.deviceDataManager.clearDeviceData(deviceType);

    // Also clear local data
    switch (deviceType) {
      case DeviceType.PULSE_OXIMETER:
        this.resultdata = null;
        break;
      case DeviceType.THERMOMETER:
        this.thermometerData = null;
        break;
      case DeviceType.BLOOD_PRESSURE:
        this.bloodPressureData = null;
        break;
      case DeviceType.ECG:
        this.ecgData = null;
        break;
      case DeviceType.STETHOSCOPE:
        this.stethoscopeData = null;
        break;
    }
  }

  /**
   * Clear all device data
   */
  clearAllDeviceData() {
    console.log('🧹 Clearing all device data');
    this.deviceDataManager.clearAllDeviceData();

    // Clear local data
    this.resultdata = null;
    this.thermometerData = null;
    this.bloodPressureData = null;
    this.ecgData = null;
    this.stethoscopeData = null;
  }

  /**
   * Get current platform info
   */
  getCurrentPlatform(): string {
    return this.deviceDataManager.getCurrentPlatform();
  }

  /**
   * Check if device is supported
   */
  isDeviceSupported(deviceType: DeviceType): boolean {
    return this.deviceDataManager.isDeviceSupported(deviceType);
  }

  /**
   * Check if device is available (for template use)
   */
  isDeviceAvailable(deviceType: string): boolean {
    const deviceTypeEnum = deviceType as DeviceType;
    return this.availableDevices.includes(deviceTypeEnum);
  }
}
