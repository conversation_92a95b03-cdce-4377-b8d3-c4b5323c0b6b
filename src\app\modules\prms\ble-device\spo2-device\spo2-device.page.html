<div class="spo2-dialog-wrapper">
    <div class="spo2-header">
        <!-- 🔋 Battery on the left -->
        <div class="spo2-battery" *ngIf="battery">
            <div class="batteryContainer">
                <div class="batteryOuter">
                    <div id="batteryLevel" [style.width.%]="battery"></div>
                </div>
                <div class="batteryBump"></div>
            </div>
        </div>

        <!-- 🧪 Centered Title -->
        <div class="spo2-title">SpO₂</div>

        <!--  Close button on the right -->
        <button class="spo2-close-btn" (click)="onDialogClose()">
      ✕
    </button>
    </div>

    <div class="spo2-percentage">SpO₂: {{ spoTowPercentage }}%</div>

    <!-- <div #chartContainer class="spo2-chart-container"></div> -->
    <div class="spo2-chart-container">
        <div class="spo2-scroll-wrapper">
            <div #chartContainer></div>
        </div>
    </div>

</div>