{"Welcome": "\\u0938\\u094D\\u0935\\u093E\\u0917\\u0924", "Railway_Employee": "\\u0930\\u0947\\u0932\\u0935\\u0947 \\u0915\\u0930\\u094D\\u092E\\u091A\\u093E\\u0930\\u0940", "Prescription": "\\u092A\\u0930\\u094D\\u091A\\u0947", "Advise": "\\u0938\\u0932\\u093E\\u0939", "Report_Start_Date_Configuration": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u0915\\u0949\\u0928\\u094D\\u092B\\u093C\\u093F\\u0917\\u0930\\u0947\\u0936\\u0928", "Report_Start_Date_Updated_Success": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915", "Issue_In_Report_Start_Date_Update": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E\\u0964", "ob_gyn": "\\u092E\\u0948\\u0902, ~doctor_name~, \\u0918\\u094B\\u0937\\u0923\\u093E \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0905\\u0932\\u094D\\u091F\\u094D\\u0930\\u093E\\u0938\\u093E\\u0909\\u0902\\u0921 \\u091B\\u0935\\u093F \\u0938\\u094D\\u0915\\u0948\\u0928\\u093F\\u0902\\u0917 \\u0915\\u0947 <br> \\u092E\\u0942\\u0932\\u094D\\u092F\\u093E\\u0902\\u0915\\u0928 \\u0915\\u093E \\u0938\\u0902\\u091A\\u093E\\u0932\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F, <br> \\u092E\\u0948\\u0902\\u0928\\u0947 \\u0915\\u093F\\u0938\\u0940 \\u092D\\u0940 \\u0924\\u0930\\u0939 \\u0938\\u0947 \\u0915\\u093F\\u0938\\u0940 \\u092D\\u0940 \\u092D\\u094D\\u0930\\u0942\\u0923 \\u0915\\u0947 \\u0932\\u093F\\u0902\\u0917 \\u0915\\u093E \\u0928 \\u0924\\u094B \\u092A\\u0924\\u093E \\u0932\\u0917\\u093E\\u092F\\u093E \\u0939\\u0948 \\u0914\\u0930 \\u0928 \\u0939\\u0940 \\u0907\\u0938\\u0915\\u093E \\u0916\\u0941\\u0932\\u093E\\u0938\\u093E \\u0915\\u093F\\u092F\\u093E \\u0939\\u0948\\u0964", "please_select": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u091A\\u0941\\u0928\\u0947", "COMPLETE": "\\u092A\\u0942\\u0930\\u094D\\u0923", "INCOMPLETE": "\\u0905\\u0927\\u0942\\u0930\\u093E", "Dashboard": "\\u090F\\u092E\\u0906\\u0908\\u090F\\u0938 \\u0921\\u0948\\u0936\\u092C\\u094B\\u0930\\u094D\\u0921", "Change_language_of_all_users_in_this_domain": "\\u0907\\u0938 \\u0921\\u094B\\u092E\\u0947\\u0928 \\u092E\\u0947\\u0902 \\u0938\\u092D\\u0940 \\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E\\u0913\\u0902 \\u0915\\u0940 \\u092D\\u093E\\u0937\\u093E \\u092C\\u0926\\u0932\\u0947\\u0902\\u0964", "FOLLOWUP": "\\u091C\\u093E\\u0901\\u091A \\u0915\\u0930\\u0928\\u093E", "NEW": "\\u0928\\u092F\\u093E", "Enter_Weight_upto_three_decimal_pionts": "\\u0938\\u0939\\u0940 \\u090A\\u0902\\u091A\\u093E\\u0908 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Height_upto_two_decimal_pionts": "\\u0938\\u0939\\u0940 \\u0935\\u091C\\u0928 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Select_Subchapter": "\\u0909\\u092A \\u0905\\u0927\\u094D\\u092F\\u093E\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Blood_Presure": "\\u0930\\u0915\\u094D\\u0924 \\u091A\\u093E\\u092A", "Ultrasound": "\\u0905\\u0932\\u094D\\u091F\\u094D\\u0930\\u093E\\u0938\\u093E\\u0909\\u0902\\u0921", "Select_country": "\\u0926\\u0947\\u0936 \\u091A\\u0941\\u0928\\u093F\\u090F", "Reconnecting": "\\u0938\\u0930\\u094D\\u0935\\u0930 \\u0915\\u094B \\u092A\\u0941\\u0928\\u0903 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930 \\u0930\\u0939\\u093E \\u0939\\u0948 ......", "Server_session_has_been_closed_Please_login_again": "\\u0938\\u0930\\u094D\\u0935\\u0930 \\u0938\\u0924\\u094D\\u0930 \\u092C\\u0902\\u0926 \\u0915\\u0930 \\u0926\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093F\\u0930 \\u092D\\u093E\\u0917 \\u0932\\u0947\\u0902\\u0964", "Make_sure_digital_pen_service_is_installed_or_running": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0921\\u093F\\u091C\\u093F\\u091F\\u0932 \\u092A\\u0947\\u0928 \\u0938\\u0947\\u0935\\u093E \\u0938\\u094D\\u0925\\u093E\\u092A\\u093F\\u0924 \\u092F\\u093E \\u091A\\u093E\\u0932\\u0942 \\u0939\\u0948\\u0964", "something_went_wrong": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E", "Mail_Copy_has_been_successfully": "\\u092E\\u0947\\u0932 \\u0915\\u0949\\u092A\\u0940 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092D\\u0947\\u091C\\u0940 \\u0917\\u0908 \\u0939\\u0948\\u0964", "Sending_Mail_Copy_Failed": "\\u092E\\u0947\\u0932 \\u0915\\u0949\\u092A\\u0940 \\u092D\\u0947\\u091C\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932\\u0964", "something_went_wrong_when_sending_mail_report": "\\u092E\\u0947\\u0932 \\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u092D\\u0947\\u091C\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u0941\\u0906", "Please_upload_files_having_extensions": "\\u090F\\u0915\\u094D\\u0938\\u091F\\u0947\\u0902\\u0936\\u0928 \\u0935\\u093E\\u0932\\u0940 \\u092B\\u093E\\u0907\\u0932\\u0947\\u0902 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Taking_Too_Long_To_Print_Retry_Later": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u092C\\u0939\\u0941\\u0924 \\u0938\\u092E\\u092F \\u0932\\u0917 \\u0930\\u0939\\u093E \\u0939\\u0948 .. \\u092C\\u093E\\u0926 \\u092E\\u0947\\u0902 \\u092A\\u0941\\u0928", "Print_Success": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0938\\u092B\\u0932\\u0924\\u093E", "No_Response_From_Thermal_Printer_Make_Sure_Thermal_Printer_Service_is_Running": "\\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0915\\u094B\\u0908 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E \\u0928\\u0939\\u0940\\u0902! \\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947\\u0935\\u093E \\u091A\\u0932 \\u0930\\u0939\\u0940 \\u0939\\u0948\\u0964", "Printer_Device_Not_Connected": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948\\u0964", "Double_Quote_Response_From_Printer": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0926\\u094B\\u0939\\u0930\\u093E \\u092D\\u093E\\u0935 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E\\u0964", "Blank_Response_From_Printer": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0930\\u093F\\u0915\\u094D\\u0924 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E\\u0964", "No_Response_From_Printer_Make_Sure_Service_is_Running": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0915\\u094B\\u0908 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E \\u0928\\u0939\\u0940\\u0902! \\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0938\\u0947\\u0935\\u093E \\u091A\\u0932 \\u0930\\u0939\\u0940 \\u0939\\u0948\\u0964", "Invalid_Complaint": "\\u0905\\u0935\\u0948\\u0927 \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924", "Please_answer_all_questions": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u092D\\u0940 \\u092A\\u094D\\u0930\\u0936\\u094D\\u0928\\u094B\\u0902 \\u0915\\u0947 \\u0909\\u0924\\u094D\\u0924\\u0930 \\u0926\\u0947\\u0902!", "Data_saved_successfully": "\\u0921\\u0947\\u091F\\u093E \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E!", "Oops_something_went_wrong": "\\u0913\\u0939! \\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E \\u0939\\u0948!", "Please_provide_medicine_name": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0926\\u0935\\u093E \\u0915\\u093E \\u0928\\u093E\\u092E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902!", "This_is_not_implemented_yet": "\\u092F\\u0939 \\u0905\\u092D\\u0940 \\u0924\\u0915 \\u0932\\u093E\\u0917\\u0942 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0941\\u0906 \\u0939\\u0948", "Recording_Fetal_Data": "\\u092D\\u094D\\u0930\\u0942\\u0923 \\u0921\\u0947\\u091F\\u093E \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0939\\u094B \\u0930\\u0939\\u093E \\u0939\\u0948", "sensor_connected_successfully": "\\u0938\\u0947\\u0902\\u0938\\u0930 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906 \\u0939\\u0948!", "error_while_taking_ECG_reading": "ECG \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0932\\u0947\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Problem_while_start_stop_scan": "\\u0938\\u094D\\u0915\\u0948\\u0928 \\u0936\\u0941\\u0930\\u0942 / \\u092C\\u0902\\u0926 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u094B\\u0902\\u0917\\u0932 \\u0915\\u094B \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902 \\u0914\\u0930 \\u092A\\u0947\\u091C \\u0915\\u094B \\u092A\\u0941\\u0928\\u0903 \\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "start_scan_status": "\\u0938\\u094D\\u0915\\u0948\\u0928 \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0940 \\u0905\\u0935\\u0938\\u094D\\u0925\\u093E", "stop_scan_status": "\\u0938\\u094D\\u0915\\u0948\\u0928 \\u0930\\u094B\\u0915\\u0928\\u0947 \\u0915\\u0940 \\u0905\\u0935\\u0938\\u094D\\u0925\\u093E", "Your_Image": "\\u0906\\u092A\\u0915\\u0940 \\u091B\\u0935\\u093F", "Mobile_number": "\\u092E\\u094B\\u092C\\u093E\\u0907\\u0932 \\u0928\\u0902\\u092C\\u0930", "Consumables": "\\u0909\\u092A\\u092D\\u094B\\u0917\\u094D\\u092F", "Data_Dump": "\\u0921\\u0947\\u091F\\u093E \\u0915\\u0940 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0932\\u093F\\u092A\\u093F \\u092C\\u0928\\u093E\\u0908 \\u0917\\u0908", "All_Doctors_in_this_domain": "\\u0907\\u0938 \\u0921\\u094B\\u092E\\u0947\\u0928 \\u092E\\u0947\\u0902 \\u0938\\u092D\\u0940 \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930", "Please_Switch_On_SpiroSensor_Device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u094D\\u092A\\u093E\\u0907\\u0930\\u094B \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0916\\u094B\\u0932\\u0947\\u0902 \\u092F\\u093E \\u0938\\u094D\\u0935\\u093F\\u091A \\u0911\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_Switch_On_StethoSensor_Device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0916\\u094B\\u0932\\u0947\\u0902 \\u092F\\u093E \\u0938\\u094D\\u0935\\u093F\\u091A \\u0911\\u0928 \\u0915\\u0930\\u0947\\u0902", "please_select_from_date_first": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0939\\u0932\\u0947 \\u0924\\u093F\\u0925\\u093F \\u0938\\u0947 \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Record_saved_successfully": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E", "Strip": "\\u092A\\u091F\\u094D\\u091F\\u0940", "Test_Result": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E", "Reference_Strip": "\\u0938\\u0902\\u0926\\u0930\\u094D\\u092D \\u092A\\u091F\\u094D\\u091F\\u0940", "Urine_Test": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Reading": "\\u092A\\u0922\\u093C\\u0928\\u093E", "Blood_Pressure": "\\u0930\\u0915\\u094D\\u0924\\u091A\\u093E\\u092A", "Rapid_Reader": "\\u0924\\u0940\\u0935\\u094D\\u0930 \\u092A\\u093E\\u0920\\u0915", "Test_Image_Not_Found": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u091B\\u0935\\u093F \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u0940", "Computing_result": "\\u0915\\u092E\\u094D\\u092A\\u094D\\u092F\\u0942\\u091F\\u093F\\u0902\\u0917 \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E", "hrs": "\\u092C\\u091C\\u0947", "AND_Test_Code": "\\u0914\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0915\\u094B\\u0921", "already_exists": "\\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "Zoom_In": "\\u091C\\u093C\\u0942\\u092E \\u0907\\u0928", "Zoom_Out": "\\u091C\\u093C\\u0942\\u092E \\u0906\\u0909\\u091F", "Rotate": "\\u0918\\u0941\\u092E\\u093E\\u090F\\u0901", "Prev": "\\u092A\\u093F\\u091B\\u0932\\u093E", "Next": "\\u0906\\u0917\\u093E\\u092E\\u0940", "Waiting_for_doctor_to_connect": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0938\\u0947 \\u091C\\u0941\\u0921\\u093C\\u0928\\u0947 \\u0915\\u093E \\u0907\\u0902\\u0924\\u091C\\u093C\\u093E\\u0930.", "Connected_with_Doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0938\\u0947 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906", "Something_went_wrong_please_relogin": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u0932\\u0949\\u0917\\u093F\\u0928 \\u0915\\u0930\\u0947\\u0902..", "Waiting_for_Patient_to_connect": "\\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930 \\u0930\\u0939\\u093E \\u0939\\u0948.", "Connected_with_Patient": "\\u0930\\u094B\\u0917\\u0940 \\u0938\\u0947 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906.", "Some_error_to_handle": "\\u0915\\u0941\\u091B \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0915\\u094B \\u0938\\u0902\\u092D\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F", "Optical_Reader_device_Not_Detected": "\\u0911\\u092A\\u094D\\u091F\\u093F\\u0915\\u0932 \\u0930\\u0940\\u0921\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u093E \\u092A\\u0924\\u093E \\u0928\\u0939\\u0940\\u0902 \\u0932\\u0917\\u093E\\u092F\\u093E \\u0917\\u092F\\u093E", "Result_cannot_be_determined": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0928\\u093F\\u0930\\u094D\\u0927\\u093E\\u0930\\u093F\\u0924 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u093F\\u092F\\u093E \\u091C\\u093E \\u0938\\u0915\\u0924\\u093E \\u0939\\u0948", "Slide_not_Present_in_tray": "\\u091F\\u094D\\u0930\\u0947 \\u092E\\u0947\\u0902 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0928\\u0939\\u0940\\u0902 \\u0938\\u094D\\u0932\\u093E\\u0907\\u0921", "Directory_not_created": "\\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936\\u093F\\u0915\\u093E \\u0928\\u0939\\u0940\\u0902 \\u092C\\u0928\\u093E\\u0908 \\u0917\\u0908", "Could_Not_Open_ROI_Image": "\\u0906\\u0930\\u0913\\u0906\\u0908 \\u091B\\u0935\\u093F \\u0928\\u0939\\u0940\\u0902 \\u0916\\u094B\\u0932 \\u0938\\u0915\\u0924\\u093E", "Invalid_Test": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Slide_Not_inserted_Properly": "\\u0938\\u094D\\u0932\\u093E\\u0907\\u0921 \\u0920\\u0940\\u0915 \\u0938\\u0947 \\u0928\\u0939\\u0940\\u0902 \\u0921\\u093E\\u0932\\u0940 \\u0917\\u0908", "Test_Type_Not_Implemented": "\\u091F\\u0947\\u0938\\u094D\\u091F \\u091F\\u093E\\u0907\\u092A \\u0932\\u093E\\u0917\\u0942 \\u0928\\u0939\\u0940\\u0902", "Could_Not_Open_Source_Image": "\\u0938\\u094D\\u0930\\u094B\\u0924 \\u091B\\u0935\\u093F \\u0928\\u0939\\u0940\\u0902 \\u0916\\u094B\\u0932 \\u0938\\u0915\\u0924\\u093E", "Something_Problem_With_Camera": "\\u0915\\u0948\\u092E\\u0930\\u093E \\u0915\\u0947 \\u0938\\u093E\\u0925 \\u0915\\u0941\\u091B \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Could_not_open_Camera": "\\u0915\\u0948\\u092E\\u0930\\u093E \\u0928\\u0939\\u0940\\u0902 \\u0916\\u094B\\u0932 \\u0938\\u0915\\u093E", "Device_connected": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0939\\u0948", "Capture": "\\u0915\\u092C\\u094D\\u091C\\u093E", "Error_with_UrineTest_Try_Again": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092E\\u0947\\u0902 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F\\u0964 \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902\\u0964", "ERROR_Failed_to_detect_test_strip": "\\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "ERROR_Time_Over_Please_insert_tray_strip_in_lessthan_30seconds": "\\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "UrineTest_Cancelled_Check_connection_camera": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930 \\u0926\\u093F\\u092F\\u093E \\u091C\\u093E\\u0924\\u093E \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u0948\\u092E\\u0930\\u0947 \\u0915\\u093E \\u0915\\u0928\\u0947\\u0915\\u094D\\u0936\\u0928 \\u091C\\u093E\\u0902\\u091A\\u0947\\u0902\\u0964", "Please_TURN_on_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u092A\\u0915\\u0930\\u0923 \\u091A\\u093E\\u0932\\u0942 \\u0915\\u0930\\u0947\\u0902", "Please_follow_the_instructions_on_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0926\\u093F\\u090F \\u0917\\u090F \\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936\\u094B\\u0902 \\u0915\\u093E \\u092A\\u093E\\u0932\\u0928 \\u0915\\u0930\\u0947\\u0902", "Connect_Device_to_another_port": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u094B \\u0926\\u0942\\u0938\\u0930\\u0947 \\u092A\\u094B\\u0930\\u094D\\u091F \\u0938\\u0947 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902\\u0964", "Device_is_incompitable": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0905\\u0915\\u094D\\u0937\\u092E \\u0939\\u0948", "Record_not_found": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E\\u0964", "ERROR_Failed_to_detect_reference_strip_Please_dont_remove_Tray_or_Strip": "\\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Caution_For_better_accuracy_retake_reding": "\\u0938\\u093E\\u0935\\u0927\\u093E\\u0928\\u0940", "Stethoscope_device_connected_successfully": "\\u0938\\u094D\\u091F\\u0947\\u0925\\u094B\\u0938\\u094D\\u0915\\u094B\\u092A \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906 \\u0939\\u0948\\u0964", "Make_sure_Stethoscope_Device_is_paired_and_switchedON": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B\\u0938\\u094D\\u0915\\u094B\\u092A \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u094B \\u091C\\u094B\\u0921\\u093C\\u093E \\u0914\\u0930 \\u091A\\u093E\\u0932\\u0942 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948\\u0964", "Make_sure_Stethoscope_device_is_paired": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B\\u0938\\u094D\\u0915\\u094B\\u092A \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092F\\u0941\\u0917\\u094D\\u092E\\u093F\\u0924 \\u0939\\u0948\\u0964", "Please_wait_Connecting_Stethoscope_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902\\u0964 \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B\\u0938\\u094D\\u0915\\u094B\\u092A \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u094B \\u091C\\u094B\\u0921\\u093C\\u0928\\u093E ...\\u0964", "Enter_Valid_LDL_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u090F\\u0932\\u0921\\u0940\\u090F\\u0932 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Failed_to_Display_Spirometry_Past_Data": "\\u0938\\u094D\\u092A\\u093F\\u0930\\u094B\\u092E\\u0947\\u091F\\u094D\\u0930\\u0940 \\u092A\\u093E\\u0938\\u094D\\u091F \\u0921\\u0947\\u091F\\u093E \\u092A\\u094D\\u0930\\u0926\\u0930\\u094D\\u0936\\u093F\\u0924 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932", "Past_Records_Fetching_Failed": "\\u0935\\u093F\\u0917\\u0924 \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u092A\\u094D\\u0930\\u093E\\u092A\\u094D\\u0924 \\u0915\\u0930\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Fail_to_save_writing_pad_content": "\\u092A\\u0948\\u0921 \\u0938\\u093E\\u092E\\u0917\\u094D\\u0930\\u0940 \\u0932\\u093F\\u0916\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0964", "Writingpad_content_successfully_saved": "\\u092A\\u0948\\u0921 \\u0938\\u093E\\u092E\\u0917\\u094D\\u0930\\u0940 \\u0932\\u093F\\u0916\\u0928\\u093E \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E\\u0964", "Offline_Data_Available": "\\u0911\\u092B\\u093C\\u0932\\u093E\\u0907\\u0928 \\u0921\\u0947\\u091F\\u093E \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927", "Makesure_ewritemate_device_is_connected": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0908-\\u0930\\u093F\\u091F\\u0947\\u092E\\u0947\\u091F \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092F\\u0942\\u090F\\u0938\\u092C\\u0940 \\u0915\\u0947\\u092C\\u0932 \\u0915\\u0947 \\u092E\\u093E\\u0927\\u094D\\u092F\\u092E \\u0938\\u0947 \\u0938\\u093F\\u0938\\u094D\\u091F\\u092E \\u0938\\u0947 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0948\\u0964", "WritingPad_Failed_to_load_close_and_open_it_again": "\\u0932\\u0947\\u0916\\u0928 \\u092A\\u0948\\u0921 \\u0932\\u094B\\u0921 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0907\\u0938\\u0947 \\u092C\\u0902\\u0926 \\u0915\\u0930\\u0947\\u0902 \\u0914\\u0930 \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0916\\u094B\\u0932\\u0947\\u0902\\u0964", "image_saved": "\\u091B\\u0935\\u093F \\u0938\\u0939\\u0947\\u091C\\u0940 \\u0917\\u0908", "Problem_in_Inserting_Consultation_Fee": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0932\\u094D\\u0915 \\u091C\\u092E\\u093E \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Problem_in_saving_Consultation_Fee": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0932\\u094D\\u0915 \\u092C\\u091A\\u093E\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Please_provide_only_integer_number": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u0947\\u0935\\u0932 \\u092A\\u0942\\u0930\\u094D\\u0923\\u093E\\u0902\\u0915 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_wait_uploading_image": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902, \\u091B\\u0935\\u093F \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0939\\u094B \\u0930\\u0939\\u0940 \\u0939\\u0948\\u2026\\u0964", "Error_while_uploading_image_Please_try_again": "\\u091B\\u0935\\u093F \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0939\\u0941\\u0908, \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902!", "Image_uploaded_successfully": "\\u091B\\u0935\\u093F \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0940 \\u0917\\u0908!", "Invalid_contact_person_lastname": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F \\u0915\\u093E \\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E \\u0905\\u092E\\u093E\\u0928\\u094D\\u092F\\u0964", "Invalid_contact_person_firstname": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F \\u0915\\u093E \\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E \\u0905\\u092E\\u093E\\u0928\\u094D\\u092F\\u0964", "Invalid_ProjectId": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u092A\\u094D\\u0930\\u094B\\u091C\\u0947\\u0915\\u094D\\u091F \\u0906\\u0908\\u0921\\u0940", "Invalid_email": "\\u0905\\u0935\\u0948\\u0927 \\u0908\\u092E\\u0947\\u0932\\u0964", "DoubleQuote_Response_From_Printer": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0926\\u094B\\u0939\\u0930\\u093E \\u092D\\u093E\\u0935 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E\\u0964", "NoResponse_From_ThermalPrinter": "\\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947 \\u0915\\u094B\\u0908 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0915\\u094D\\u0930\\u093F\\u092F\\u093E \\u0928\\u0939\\u0940\\u0902! \\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u0938\\u0947\\u0935\\u093E \\u091A\\u0932 \\u0930\\u0939\\u0940 \\u0939\\u0948\\u0964", "An_unknown_error_occurred_while_trying_to_publish_your_video_Please_try_again": "\\u0906\\u092A\\u0915\\u0947 \\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u0915\\u094B \\u092A\\u094D\\u0930\\u0915\\u093E\\u0936\\u093F\\u0924 \\u0915\\u0930\\u0928\\u0947 \\u0915\\u093E \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u090F\\u0915 \\u0905\\u091C\\u094D\\u091E\\u093E\\u0924 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0939\\u0941\\u0908\\u0964 \\u092C\\u093E\\u0926 \\u092E\\u0947\\u0902 \\u092A\\u0941\\u0928", "is_connected_and_notbeing_used_by_another_application_and_try_again": "\\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906 \\u0939\\u0948 \\u0914\\u0930 \\u0915\\u093F\\u0938\\u0940 \\u0905\\u0928\\u094D\\u092F \\u090F\\u092A\\u094D\\u0932\\u093F\\u0915\\u0947\\u0936\\u0928 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0909\\u092A\\u092F\\u094B\\u0917 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u093F\\u092F\\u093E \\u091C\\u093E \\u0930\\u0939\\u093E \\u0939\\u0948 \\u0914\\u0930 \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_check_that_your_webcam": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u091C\\u093E\\u0902\\u091A\\u0947\\u0902 \\u0915\\u093F \\u0906\\u092A\\u0915\\u093E \\u0935\\u0947\\u092C\\u0915\\u0948\\u092E", "Failed_to_get_access_to_your_camera_or_microphone": "\\u0906\\u092A\\u0915\\u0947 \\u0915\\u0948\\u092E\\u0930\\u0947 \\u092F\\u093E \\u092E\\u093E\\u0907\\u0915\\u094D\\u0930\\u094B\\u092B\\u093C\\u094B\\u0928 \\u0924\\u0915 \\u092A\\u0939\\u0941\\u0902\\u091A\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0964", "Publishing_your_video_failed_This_due_to_restrictive_firewall": "\\u0906\\u092A\\u0915\\u093E \\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u092A\\u094D\\u0930\\u0915\\u093E\\u0936\\u093F\\u0924 \\u0915\\u0930\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E\\u0964 \\u092F\\u0939 \\u092A\\u094D\\u0930\\u0924\\u093F\\u092C\\u0902\\u0927\\u093E\\u0924\\u094D\\u092E\\u0915 \\u092B\\u093C\\u093E\\u092F\\u0930\\u0935\\u0949\\u0932 \\u0915\\u0947 \\u0915\\u093E\\u0930\\u0923 \\u0939\\u094B \\u0938\\u0915\\u0924\\u093E \\u0939\\u0948\\u0964", "Publishing_your_video_failed": "\\u0906\\u092A\\u0915\\u093E \\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u092A\\u094D\\u0930\\u0915\\u093E\\u0936\\u093F\\u0924 \\u0915\\u0930\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E\\u0964 \\u0906\\u092A \\u0907\\u0902\\u091F\\u0930\\u0928\\u0947\\u091F \\u0938\\u0947 \\u0928\\u0939\\u0940\\u0902 \\u091C\\u0941\\u0921\\u093C\\u0947 \\u0939\\u0948\\u0902\\u0964", "Your_publisher_lost_its_connection_try_publishing_again": "\\u0906\\u092A\\u0915\\u0947 \\u092A\\u094D\\u0930\\u0915\\u093E\\u0936\\u0915 \\u0928\\u0947 \\u0905\\u092A\\u0928\\u093E \\u0915\\u0928\\u0947\\u0915\\u094D\\u0936\\u0928 \\u0916\\u094B \\u0926\\u093F\\u092F\\u093E\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0905\\u092A\\u0928\\u093E \\u0907\\u0902\\u091F\\u0930\\u0928\\u0947\\u091F \\u0915\\u0928\\u0947\\u0915\\u094D\\u0936\\u0928 \\u091C\\u093E\\u0902\\u091A\\u0947\\u0902 \\u0914\\u0930 \\u092A\\u0941\\u0928", "Media_access_denied": "\\u092E\\u0940\\u0921\\u093F\\u092F\\u093E \\u0915\\u0940 \\u092A\\u0939\\u0941\\u0902\\u091A \\u0905\\u0938\\u094D\\u0935\\u0940\\u0915\\u0943\\u0924", "Please_allow_access_to_Camera_Microphone": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u0948\\u092E\\u0930\\u093E \\u0914\\u0930 \\u092E\\u093E\\u0907\\u0915\\u094D\\u0930\\u094B\\u092B\\u093C\\u094B\\u0928 \\u0924\\u0915 \\u092A\\u0939\\u0941\\u0901\\u091A \\u0915\\u0940 \\u0905\\u0928\\u0941\\u092E\\u0924\\u093F \\u0926\\u0947\\u0902 \\u0914\\u0930 \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u0915\\u093E\\u0936\\u0928 \\u0915\\u093E \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902\\u0964", "ECG_Unreachable": "ECG \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0939\\u0941\\u0902\\u091A \\u0938\\u0947 \\u092C\\u093E\\u0939\\u0930 \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u092A\\u0915\\u0930\\u0923 \\u092A\\u093E\\u0938 \\u092E\\u0947\\u0902 \\u0930\\u0916\\u0947\\u0902 \\u0914\\u0930 \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u092A\\u0922\\u093C\\u0928\\u093E \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0947\\u0902\\u0964", "Provisional": "\\u0905\\u0928\\u0902\\u0924\\u093F\\u092E", "Your_Generate_Password_is": "\\u0906\\u092A\\u0915\\u093E \\u091C\\u0928\\u0930\\u0947\\u091F \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0939\\u0948", "Please_wait": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902", "You_cannot_start_pending_or_review_consultation": "\\u0906\\u092A \\u0932\\u0902\\u092C\\u093F\\u0924 \\u092F\\u093E \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0930\\u0942 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947\\u0964", "cannot_start_reviewed_consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0915\\u0940 \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u0936\\u0941\\u0930\\u0942 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947", "If_Evolute_Service_Utility_is_Running": "\\u092F\\u0926\\u093F Evolute Service / \\u0909\\u092A\\u092F\\u094B\\u0917\\u093F\\u0924\\u093E \\u091A\\u0932 \\u0930\\u0939\\u0940 \\u0939\\u0948", "Select_Complaint": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Pharmacy_Name_From_List": "\\u0938\\u0942\\u091A\\u0940 \\u0938\\u0947 \\u092B\\u093E\\u0930\\u094D\\u092E\\u0947\\u0938\\u0940 \\u0915\\u093E \\u0928\\u093E\\u092E \\u091A\\u0941\\u0928\\u0947\\u0902", "Wait_while_Printing": "\\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902 .....", "Please_Wait_while_Printing": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902 ...\\u0964", "Mobile": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E", "PID": "\\u092A\\u0940\\u0906\\u0908\\u0921\\u0940", "record_already_exist": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "Please_Provide_Projectid": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u094B\\u091C\\u0947\\u0915\\u094D\\u091F \\u0906\\u0908\\u0921\\u0940 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Record_already_exists": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "problem_while_inserting": "\\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_occured_while_updating": "\\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_inserting_Brand": "\\u092C\\u094D\\u0930\\u093E\\u0902\\u0921 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_inserting_Drug_Class": "\\u0921\\u094D\\u0930\\u0917 \\u0915\\u094D\\u0932\\u093E\\u0938 \\u0932\\u0917\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_occured_while_inserting_Drug_Form": "\\u0921\\u094D\\u0930\\u0917 \\u092B\\u0949\\u0930\\u094D\\u092E \\u0921\\u093E\\u0932\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Approve_Number_cannot_be_greater_than_Requested": "\\u0938\\u094D\\u0935\\u0940\\u0915\\u0943\\u0924 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E \\u0905\\u0928\\u0941\\u0930\\u094B\\u0927 \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u094B \\u0938\\u0915\\u0924\\u0940", "problem_occured_while_inserting_category": "\\u0936\\u094D\\u0930\\u0947\\u0923\\u0940 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "already_exist_Please_Try_another_name": "\\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948 \\u0915\\u0943\\u092A\\u092F\\u093E \\u090F\\u0915 \\u0914\\u0930 \\u0928\\u093E\\u092E \\u0906\\u091C\\u093C\\u092E\\u093E\\u090F\\u0902", "problem_occured_while_inserting_lab": "\\u092A\\u094D\\u0930\\u092F\\u094B\\u0917\\u0936\\u093E\\u0932\\u093E \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "This_Pharmacy_Detail_already_exists": "\\u092F\\u0939 \\u092B\\u093E\\u0930\\u094D\\u092E\\u0947\\u0938\\u0940 \\u0935\\u093F\\u0935\\u0930\\u0923 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "Record_is_not_available_to_update": "\\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "Doctor_Record_Not_Found": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u093E \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E", "Doctor_registration_limit_has_been_exceeded": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0930\\u0923 \\u0915\\u0940 \\u0938\\u0940\\u092E\\u093E \\u092A\\u093E\\u0930 \\u0915\\u0930 \\u091A\\u0941\\u0915\\u0947 \\u0939\\u0948\\u0902", "problem_while_inserting_in_doctorprofile": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932 \\u092E\\u0947\\u0902 \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_in_loginDetails": "\\u0932\\u0949\\u0917\\u093F\\u0928 \\u0935\\u093F\\u0935\\u0930\\u0923 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_occured_while_updating_doctor_profile": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932 \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "You_can_not_enable_Doctor_Given_limit_has_been_exceeded": "\\u0906\\u092A \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u094B \\u0938\\u0915\\u094D\\u0937\\u092E \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0939\\u0948\\u0902, \\u0926\\u0940 \\u0917\\u0908 \\u0938\\u0940\\u092E\\u093E \\u092A\\u093E\\u0930 \\u0915\\u0930 \\u0926\\u0940 \\u0917\\u0908 \\u0939\\u0948", "Nurse_registration_limit_has_been_exceeded": "\\u0928\\u0930\\u094D\\u0938 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0930\\u0923 \\u0915\\u0940 \\u0938\\u0940\\u092E\\u093E \\u092A\\u093E\\u0930 \\u0915\\u0930 \\u0917\\u0908 \\u0939\\u0948", "problem_while_inserting_nurse_profile": "\\u0928\\u0930\\u094D\\u0938 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932 \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "error_while_storing_nurse_data": "\\u0928\\u0930\\u094D\\u0938 \\u0921\\u0947\\u091F\\u093E \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u0940\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "You_can_not_enable_Nurse_Given_limit_has_been_exceeded": "\\u0906\\u092A \\u0928\\u0930\\u094D\\u0938 \\u0915\\u094B \\u0938\\u0915\\u094D\\u0937\\u092E \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0939\\u0948\\u0902,  \\u0926\\u0940 \\u0917\\u0908 \\u0938\\u0940\\u092E\\u093E \\u092A\\u093E\\u0930 \\u0915\\u0930 \\u0926\\u0940 \\u0917\\u0908 \\u0939\\u0948", "problem_while_getting_data": "\\u0921\\u0947\\u091F\\u093E \\u092A\\u094D\\u0930\\u093E\\u092A\\u094D\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "disconnected": "\\u0905\\u0932\\u0917 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E", "completed": "\\u092A\\u0942\\u0930\\u093E \\u0915\\u0930 \\u0932\\u093F\\u092F\\u093E \\u0939\\u0948", "failure": "\\u0905\\u0938\\u092B\\u0932\\u0924\\u093E", "suspended": "\\u092C\\u0930\\u094D\\u0916\\u093E\\u0938\\u094D\\u0924 \\u0915\\u0930 \\u0926\\u093F\\u092F\\u093E", "resumed": "\\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0936\\u0941\\u0930\\u0942", "failure_to_update": "\\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0924\\u093E", "Cancel": "\\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930\\u0928\\u093E", "suspendedbydoctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0928\\u093F\\u0932\\u0902\\u092C\\u093F\\u0924", "failure_to_suspend": "\\u0938\\u0938\\u094D\\u092A\\u0947\\u0902\\u0921 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0924\\u093E", "successfully_sent_this_case_for_review": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0907\\u0938 \\u092E\\u093E\\u092E\\u0932\\u0947 \\u0915\\u094B \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0947 \\u0932\\u093F\\u090F \\u092D\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E", "failure_to_send_review": "\\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u092D\\u0947\\u091C\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0924\\u093E", "Please_provide_consultationid_and_reviewby": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0914\\u0930 \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Provide_all_the_details": "\\u0938\\u092D\\u0940 \\u0935\\u093F\\u0935\\u0930\\u0923 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Error_while_inserting_data": "\\u0921\\u0947\\u091F\\u093E \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Provide_valid_Id": "\\u0935\\u0948\\u0927 \\u0906\\u0908\\u0921\\u0940 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Some_slots_are_already_available_in_the_selected_time_range_so_Please_modify_your_selection": "\\u0915\\u0941\\u091B \\u0938\\u094D\\u0932\\u0949\\u091F \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u091A\\u092F\\u0928\\u093F\\u0924 \\u0938\\u092E\\u092F \\u0938\\u0940\\u092E\\u093E \\u092E\\u0947\\u0902 \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0939\\u0948\\u0902 \\u0907\\u0938\\u0932\\u093F\\u090F \\u0915\\u0943\\u092A\\u092F\\u093E \\u0905\\u092A\\u0928\\u093E \\u091A\\u092F\\u0928 \\u0938\\u0902\\u0936\\u094B\\u0927\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "slots_already_available": "\\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0938\\u094D\\u0932\\u0949\\u091F", "error_while_adding_slots": "\\u0938\\u094D\\u0932\\u0949\\u091F\\u094D\\u0938 \\u091C\\u094B\\u0921\\u093C\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "slots_not_found": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u0947", "success": "\\u0938\\u092B\\u0932\\u0924\\u093E", "Wrong_Request": "\\u0917\\u0932\\u0924 \\u0905\\u0928\\u0941\\u0930\\u094B\\u0927", "Comment_saved_successfully": "\\u091F\\u093F\\u092A\\u094D\\u092A\\u0923\\u0940 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u0940 \\u0917\\u0908", "Comment_saving_failed": "\\u091F\\u093F\\u092A\\u094D\\u092A\\u0923\\u0940 \\u0938\\u0939\\u0947\\u091C\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "failed": "\\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "This_medicine_is_already_exist": "\\u092F\\u0939 \\u0926\\u0935\\u093E \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "problem_occured_while_inserting_bmq_q1": "bmq q1 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "exception_occured_while_inserting_bmq_q1": "bmq q1 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0905\\u092A\\u0935\\u093E\\u0926 \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0906", "problem_occured_while_updating_bmq_q1": "bmq q1 \\u0915\\u094B \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_bmq_q1": "bmq q1 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_inserting_bmq_q3": "bmq q3 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "exception_occured_while_inserting_bmq_q3": "bmq q3 \\u0921\\u093E\\u0932\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0905\\u092A\\u0935\\u093E\\u0926 \\u0939\\u0941\\u0906", "problem_occured_while_updating_bmq_q3": "bmq q3 \\u0915\\u094B \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_bmq_q3": "bmq q3 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_inserting_bmq_q2": "bmq q2 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "exception_occured_while_inserting_bmq_q2": "bmq q2 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0905\\u092A\\u0935\\u093E\\u0926 \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0906", "problem_occured_while_updating_bmq_q2": "bmq q2 \\u0915\\u094B \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_bmq_q2": "bmq q2 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_inserting_complaints": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "record(s)_already_exist": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 (s) \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948\\u0902", "problem_occured_while_updating_complaint": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_complaint": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "diagnosis_already_prescribed": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0928\\u093F\\u0930\\u094D\\u0927\\u093E\\u0930\\u093F\\u0924 \\u0939\\u0948", "ICD_code_already_present": "\\u0906\\u0908\\u0938\\u0940\\u0921\\u0940 \\u0915\\u094B\\u0921 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0939\\u0948", "problem_occured_while_inserting_diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_updating_diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u0915\\u094B \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u0915\\u094B \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_inserting_recommendations": "\\u0938\\u093F\\u092B\\u093E\\u0930\\u093F\\u0936\\u0947\\u0902 \\u0921\\u093E\\u0932\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_updating_recommendations": "\\u0938\\u093F\\u092B\\u093E\\u0930\\u093F\\u0936\\u094B\\u0902 \\u0915\\u094B \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_deleteing_recommendations": "\\u0938\\u093F\\u092B\\u093E\\u0930\\u093F\\u0936\\u094B\\u0902 \\u0915\\u094B \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_inserting_invetigation": "\\u091C\\u093E\\u0901\\u091A \\u092A\\u0921\\u093C\\u0924\\u093E\\u0932  \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "test(s)_already_prescribed": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0928\\u093F\\u0930\\u094D\\u0927\\u093E\\u0930\\u093F\\u0924", "problem_occured_while_updating_investigation": "\\u091C\\u093E\\u0902\\u091A \\u0905\\u0926\\u094D\\u092F\\u0924\\u0928 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_deleting_investigation": "\\u091C\\u093E\\u0902\\u091A \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "Not_Sufficient_Stock_contact_admin": "\\u092A\\u0930\\u094D\\u092F\\u093E\\u092A\\u094D\\u0924 \\u0938\\u094D\\u091F\\u0949\\u0915 \\u0928\\u0939\\u0940\\u0902 \\u0935\\u094D\\u092F\\u0935\\u0938\\u094D\\u0925\\u093E\\u092A\\u0915 \\u0938\\u0947 \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0915\\u0930\\u0947\\u0902", "problem_while_inserting_ecg_data_into_master_table": "\\u092E\\u093E\\u0938\\u094D\\u091F\\u0930 \\u091F\\u0947\\u092C\\u0932 \\u092E\\u0947\\u0902 ecg data \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_ecg_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 ecg \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0938\\u092E\\u094D\\u092E\\u093F\\u0932\\u093F\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_stetho_data_into_master_table": "\\u092E\\u093E\\u0938\\u094D\\u091F\\u0930 \\u091F\\u0947\\u092C\\u0932 \\u092E\\u0947\\u0902 \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B \\u0921\\u0947\\u091F\\u093E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_stetho_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B \\u092B\\u093E\\u0907\\u0932 \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_spo2_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 spo2 \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_spiro_data_into_master_table": "\\u092E\\u093E\\u0938\\u094D\\u091F\\u0930 \\u091F\\u0947\\u092C\\u0932 \\u092E\\u0947\\u0902 \\u0938\\u094D\\u092A\\u093E\\u0907\\u0930\\u094B \\u0921\\u0947\\u091F\\u093E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_fetal_data_into_master_table": "\\u092E\\u093E\\u0938\\u094D\\u091F\\u0930 \\u091F\\u0947\\u092C\\u0932 \\u092E\\u0947\\u0902 \\u092D\\u094D\\u0930\\u0942\\u0923 \\u0915\\u093E \\u0921\\u0947\\u091F\\u093E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_fetal_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 \\u092D\\u094D\\u0930\\u0942\\u0923 \\u0915\\u093E \\u0928\\u093E\\u092E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_RETRIVING_MAX_FROM_FETAL_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 FETAL \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915\\u0924\\u092E RETRIVING \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_RETRIVING_MAX_FROM_stetho_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B \\u092B\\u093C\\u093E\\u0907\\u0932\\u0928\\u093E\\u092E \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915\\u0924\\u092E MAX RETRIVING \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_RETRIVING_MAX_FROM_ECG_BLE_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 ECG BLE \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915\\u0924\\u092E MAX RETRIVING \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_RETRIVING_MAX_FROM_ECG_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 ECG \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915\\u0924\\u092E RETRIVING \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_RETRIVING_MAX_FROM_spo2_filename_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 spo2 \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u093E\\u092E \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915\\u0924\\u092E \\u0938\\u094D\\u0915\\u094B\\u0930 \\u092A\\u094D\\u0930\\u093E\\u092A\\u094D\\u0924 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "problem_while_inserting_patient_monitor_data_into_master_table": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0928\\u093F\\u0917\\u0930\\u093E\\u0928\\u0940 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u092E\\u093E\\u0938\\u094D\\u091F\\u0930 \\u091F\\u0947\\u092C\\u0932 \\u092E\\u0947\\u0902 \\u0921\\u0947\\u091F\\u093E \\u0915\\u0940 \\u0928\\u093F\\u0917\\u0930\\u093E\\u0928\\u0940", "problem_while_inserting_patient_Monit_FileName_into_database": "\\u0921\\u0947\\u091F\\u093E\\u092C\\u0947\\u0938 \\u092E\\u0947\\u0902 \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0928\\u093F\\u0917\\u0930\\u093E\\u0928\\u0940 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u092B\\u093E\\u0907\\u0932\\u0928\\u093E\\u092E \\u0921\\u093E\\u0932\\u0928\\u0947 \\u0915\\u0947 \\u0926\\u094C\\u0930\\u093E\\u0928 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "saved": "\\u092C\\u091A\\u093E\\u092F\\u093E", "problem_occured_while_inserting_referral": "\\u0930\\u0947\\u092B\\u0930\\u0932 \\u0921\\u093E\\u0932\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "problem_occured_while_updating_referrals": "\\u0930\\u0947\\u092B\\u0930\\u0932 \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0939\\u0941\\u0908", "problem_occured_while_deleting_referrals": "\\u0930\\u0947\\u092B\\u0930\\u0932 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0939\\u0941\\u0908", "Can't_find_dependent_libraries": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0932\\u093E\\u0907\\u092C\\u094D\\u0930\\u0947\\u0930\\u0940  \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u0940!", "Finger_does_not_matched": "\\u0909\\u0902\\u0917\\u0932\\u0940 \\u092E\\u0947\\u0932 \\u0928\\u0939\\u0940\\u0902 \\u0916\\u093E\\u0924\\u0940!", "An_error_occurred_while_loading_device_libraries": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0932\\u093E\\u0907\\u092C\\u094D\\u0930\\u0947\\u0930\\u0940 \\u0932\\u094B\\u0921 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u090F\\u0915 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0939\\u0941\\u0908", "Place_your_finger_properly": "\\u0905\\u092A\\u0928\\u0940 \\u0909\\u0902\\u0917\\u0932\\u0940 \\u0920\\u0940\\u0915 \\u0938\\u0947 \\u0930\\u0916\\u0947\\u0902", "No_error": "\\u0915\\u094B\\u0908 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0928\\u0939\\u0940\\u0902", "JSGFPLib_object_creation_failed": "JSGFPLib \\u0911\\u092C\\u094D\\u091C\\u0947\\u0915\\u094D\\u091F \\u0928\\u093F\\u0930\\u094D\\u092E\\u093E\\u0923 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Function_call_failed": "\\u092B\\u093C\\u0902\\u0915\\u094D\\u0936\\u0928 \\u0915\\u0949\\u0932 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Invalid_parameter_used": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u0915\\u093E \\u0909\\u092A\\u092F\\u094B\\u0917 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E", "Not_used_function": "\\u092B\\u093C\\u0902\\u0915\\u094D\\u0936\\u0928-\\u092B\\u0932\\u0928", "DLL_loading_failed": "DLL \\u0932\\u094B\\u0921\\u093F\\u0902\\u0917 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u0940", "Device_driver_loading_failed": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0921\\u094D\\u0930\\u093E\\u0907\\u0935\\u0930 \\u0932\\u094B\\u0921 \\u0915\\u0930\\u0928\\u093E \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Algorithm_DLL_loading_failed": "\\u090F\\u0932\\u094D\\u0917\\u094B\\u0930\\u093F\\u0925\\u094D\\u092E DLL \\u0932\\u094B\\u0921\\u093F\\u0902\\u0917 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u0940", "Cannot_find_driver_sys_file": "\\u0921\\u094D\\u0930\\u093E\\u0907\\u0935\\u0930 sys \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u0940", "Chip_initialization_failed": "\\u091A\\u093F\\u092A \\u0906\\u0930\\u0902\\u092D\\u0940\\u0915\\u0930\\u0923 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Image_data_lost": "\\u091B\\u0935\\u093F \\u0921\\u0947\\u091F\\u093E \\u0916\\u094B \\u0917\\u092F\\u093E", "Image_capture_timeout": "\\u091B\\u0935\\u093F \\u0909\\u0924\\u093E\\u0930\\u0928\\u0947 \\u0915\\u093E \\u0938\\u092E\\u092F \\u0938\\u092E\\u093E\\u092A\\u094D\\u0924", "Device_not_found": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E", "Driver_file_load_failed": "\\u0921\\u094D\\u0930\\u093E\\u0907\\u0935\\u0930 \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0932\\u094B\\u0921 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Wrong_image": "\\u0917\\u0932\\u0924 \\u091B\\u0935\\u093F", "Lack_of_USB_bandwidth": "USB \\u092C\\u0948\\u0902\\u0921\\u0935\\u093F\\u0921\\u094D\\u0925 \\u0915\\u0940 \\u0915\\u092E\\u0940", "Device_is_already_opened": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u0916\\u094B\\u0932\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Serial_number_does_not_exist": "\\u0938\\u0940\\u0930\\u093F\\u092F\\u0932 \\u0928\\u0902\\u092C\\u0930 \\u092E\\u094C\\u091C\\u0942\\u0926 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "Unsupported_device_Extract_&_Matching_Error_Codes": "\\u0905\\u0938\\u092E\\u0930\\u094D\\u0925\\u093F\\u0924 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0928\\u093F\\u0915\\u093E\\u0932\\u0947\\u0902 \\u0914\\u0930 \\u092E\\u093F\\u0932\\u093E\\u0928 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0915\\u094B\\u0921", "Inadequate_number_of_minutiae": "\\u0928\\u093E\\u092C\\u093E\\u0932\\u093F\\u0917 \\u0915\\u0940 \\u0905\\u092A\\u0930\\u094D\\u092F\\u093E\\u092A\\u094D\\u0924 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E", "Wrong_template_type": "\\u0917\\u0932\\u0924 \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u092A\\u094D\\u0930\\u0915\\u093E\\u0930", "Error_in_decoding_template_1": "\\u091F\\u0947\\u092E\\u094D\\u092A\\u094D\\u0932\\u0947\\u091F 1 \\u0915\\u094B \\u0921\\u093F\\u0915\\u094B\\u0921 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Error_in_decoding_template_2": "\\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F 2 \\u0915\\u094B \\u0921\\u093F\\u0915\\u094B\\u0921 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Extraction_failed": "\\u0928\\u093F\\u0937\\u094D\\u0915\\u0930\\u094D\\u0937\\u0923 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Matching_failed": "\\u092E\\u093F\\u0932\\u093E\\u0928 \\u0935\\u093F\\u092B\\u0932 \\u0930\\u0939\\u093E", "Undefined_error_condition": "\\u0905\\u092A\\u0930\\u093F\\u092D\\u093E\\u0937\\u093F\\u0924 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0938\\u094D\\u0925\\u093F\\u0924\\u093F!", "Place_finger_on_device": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0909\\u0902\\u0917\\u0932\\u0940 \\u0930\\u0916\\u0947\\u0902!", "Finger_print_captured_successfully": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092A\\u0915\\u0921\\u093C\\u093E \\u0917\\u092F\\u093E!", "Error_while_parsing_finger_print_data": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0921\\u0947\\u091F\\u093E \\u092A\\u093E\\u0930\\u094D\\u0938 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F!", "Error_while_register_finger_print": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0930\\u091C\\u093F\\u0938\\u094D\\u091F\\u0930 \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F!", "Please_place_registered_finger_on_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0909\\u0902\\u0917\\u0932\\u0940 \\u0930\\u0916\\u0947\\u0902!", "Error_while_verifying_finger_print": "\\u0909\\u0902\\u0917\\u0932\\u0940 \\u0915\\u0940 \\u091B\\u093E\\u092A \\u0915\\u0940 \\u092A\\u0941\\u0937\\u094D\\u091F\\u093F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Finger_print_not_registered": "\\u0909\\u0902\\u0917\\u0932\\u0940 \\u0915\\u0940 \\u091B\\u093E\\u092A \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0928\\u0939\\u0940\\u0902", "View_Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u0926\\u0947\\u0916\\u0947\\u0902", "Start_Debugging": "\\u0921\\u0940\\u092C\\u0917 \\u0915\\u0930\\u0928\\u093E \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0915\\u0930\\u0947\\u0902", "Stop_Debugging": "\\u0921\\u093F\\u092C\\u0917\\u093F\\u0902\\u0917 \\u092C\\u0902\\u0926 \\u0915\\u0930\\u094B", "Loading_Camera": "\\u0915\\u0948\\u092E\\u0930\\u093E \\u0932\\u094B\\u0921 \\u0939\\u094B \\u0930\\u0939\\u093E \\u0939\\u0948", "Camera": "\\u0915\\u0948\\u092E\\u0930\\u093E", "Retry": "\\u092A\\u0941\\u0928", "Take_snapshot": "\\u0924\\u0941\\u0930\\u0902\\u0924 \\u092B\\u094B\\u091F\\u094B \\u0932\\u0947\\u0902", "Retake": "\\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0932\\u0947\\u0928\\u093E", "Please_connect_camera": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u0948\\u092E\\u0930\\u093E \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902", "Please_allow_access_to_camera": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u0948\\u092E\\u0930\\u0947 \\u0924\\u0915 \\u092A\\u0939\\u0941\\u0902\\u091A \\u0915\\u0940 \\u0905\\u0928\\u0941\\u092E\\u0924\\u093F \\u0926\\u0947\\u0902", "Make_sure_camera_is_connected_and_accessible": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0915\\u0948\\u092E\\u0930\\u093E \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906 \\u0939\\u0948 \\u0914\\u0930 \\u0938\\u0941\\u0932\\u092D \\u0939\\u0948", "Patient_Delivery_Address": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u093E \\u092A\\u0924\\u093E", "Phone_Number": "\\u092B\\u093C\\u094B\\u0928 \\u0928\\u0902\\u092C\\u0930", "Please_Enter_The_Phone_Number": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u094B\\u0928 \\u0928\\u0902\\u092C\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Please_Select_The_Pharmacy_Name": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093E\\u0930\\u094D\\u092E\\u0947\\u0938\\u0940 \\u0928\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Print_in_Thermal_Printer": "\\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930 \\u092E\\u0947\\u0902 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0930\\u0947\\u0902", "Print": "\\u091B\\u093E\\u092A", "Test_Name": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0915\\u093E \\u0928\\u093E\\u092E", "Units": "\\u0907\\u0915\\u093E\\u0907\\u092F\\u094B\\u0902", "Results": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E", "Reference_Value_Range": "\\u0938\\u0902\\u0926\\u0930\\u094D\\u092D \\u092E\\u0942\\u0932\\u094D\\u092F \\u0938\\u0940\\u092E\\u093E", "Invoice": "\\u092C\\u0940\\u091C\\u0915", "Price": "\\u092E\\u0942\\u0932\\u094D\\u092F", "Description": "\\u0935\\u093F\\u0935\\u0930\\u0923", "Parameter": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930", "Sr": {"No": {"": "\\u0905\\u0928\\u0941 \\u0915\\u094D\\u0930\\u092E\\u093E\\u0902\\u0915\\u0964"}}, "This_is_an_electronically_generated_invoice": "\\u092F\\u0939 \\u090F\\u0915 \\u0907\\u0932\\u0947\\u0915\\u094D\\u091F\\u094D\\u0930\\u0949\\u0928\\u093F\\u0915 \\u0930\\u0942\\u092A \\u0938\\u0947 \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u091A\\u093E\\u0932\\u093E\\u0928 \\u0939\\u0948", "Additional_Notes": "\\u0905\\u0924\\u093F\\u0930\\u093F\\u0915\\u094D\\u0924 \\u0928\\u094B\\u091F\\u094D\\u0938", "Test_fees": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0936\\u0941\\u0932\\u094D\\u0915", "Consultation_Fee": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0932\\u094D\\u0915", "Total": "\\u0938\\u0902\\u092A\\u0942\\u0930\\u094D\\u0923", "years": "\\u0935\\u0930\\u094D\\u0937", "months": "\\u092E\\u0939\\u0940\\u0928\\u0947", "days": "\\u0926\\u093F\\u0928", "Capture_Finger_Print": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0948\\u092A\\u094D\\u091A\\u0930 \\u0915\\u0930\\u0947\\u0902", "Consultation_Fee_Saved_Successfully": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0932\\u094D\\u0915 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0902\\u091A\\u092F \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E", "OR": "\\u092F\\u093E", "Please_select_nurse": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0928\\u0930\\u094D\\u0938 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "From_Hour": "\\u0918\\u0902\\u091F\\u0947 \\u0938\\u0947", "From_Minute": "\\u092E\\u093F\\u0928\\u091F \\u0938\\u0947", "Till_Hour": "\\u0924\\u0915 \\u0915\\u093E \\u0938\\u092E\\u092F", "Till_Minute": "\\u0924\\u0915 \\u092E\\u093F\\u0928\\u091F", "please_select_chapter": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0905\\u0927\\u094D\\u092F\\u093E\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_select_diagnosis_category": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0928\\u093F\\u0926\\u093E\\u0928 \\u0936\\u094D\\u0930\\u0947\\u0923\\u0940 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_select_subchapter": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u092A \\u0905\\u0927\\u094D\\u092F\\u093E\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Referral": "\\u0930\\u0947\\u092B\\u0930\\u0932 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Category": "\\u0936\\u094D\\u0930\\u0947\\u0923\\u0940 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Recommendations": "\\u0905\\u0928\\u0941\\u0936\\u0902\\u0938\\u093E\\u0913\\u0902 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Lab": "\\u0932\\u0948\\u092C \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Test": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Instruction": "\\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Medicine": "\\u0926\\u0935\\u093E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Chapter": "\\u0905\\u0927\\u094D\\u092F\\u093E\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Recent_Diagnosis": "\\u0939\\u093E\\u0932 \\u0939\\u0940 \\u092E\\u0947\\u0902 \\u0928\\u093F\\u0926\\u093E\\u0928", "Click_On_Diagnosis_Name_To_Add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0928\\u093F\\u0926\\u093E\\u0928 \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "click_on_complaint_name_to_add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Recent_Complaints": "\\u0939\\u093E\\u0932 \\u0915\\u0940 \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902\\u0964(\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902)", "Since": "\\u091C\\u092C\\u0938\\u0947", "Complaint_Testing": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Recently_Prescribed": "\\u0939\\u093E\\u0932 \\u0939\\u0940 \\u092E\\u0947\\u0902 \\u0928\\u093F\\u0930\\u094D\\u0927\\u093E\\u0930\\u093F\\u0924", "click_on_medicine_name_to_add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0926\\u0935\\u093E \\u0915\\u0947 \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Recent_investigations": "\\u0939\\u093E\\u0932 \\u0915\\u0940 \\u091C\\u093E\\u0902\\u091A", "click_on_investigations_name_to_add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u091C\\u093E\\u0902\\u091A \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Recent_instructions": "\\u0939\\u093E\\u0932 \\u0915\\u0947 \\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936", "click_on_instructions_name_to_add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936\\u094B\\u0902 \\u0915\\u0947 \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Recently_Referred": "\\u0939\\u093E\\u0932 \\u0939\\u0940 \\u092E\\u0947\\u0902 \\u0930\\u0947\\u092B\\u0930 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E", "click_on_referral_name_to_add": "\\u091C\\u094B\\u0921\\u093C\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0930\\u0947\\u092B\\u0930\\u0932 \\u0928\\u093E\\u092E \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Hours": "\\u0918\\u0902\\u091F\\u0947", "Days": "\\u0926\\u093F\\u0928", "Weeks": "\\u0938\\u092A\\u094D\\u0924\\u093E\\u0939", "Months": "\\u092E\\u0939\\u0940\\u0928\\u0947", "Years": "\\u0935\\u0930\\u094D\\u0937", "Chapter": "\\u0905\\u0927\\u094D\\u092F\\u093E\\u092F", "Subchapter": "\\u0909\\u092A \\u0905\\u0927\\u094D\\u092F\\u093E\\u092F", "Chapter_wise": "\\u0905\\u0927\\u094D\\u092F\\u093E\\u092F \\u0935\\u093E\\u0930", "Code": "\\u0915\\u094B\\u0921", "Upload_Domain_Logo": "\\u0921\\u094B\\u092E\\u0947\\u0928 \\u0932\\u094B\\u0917\\u094B \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Parameter_Fee_Configuration": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u0936\\u0941\\u0932\\u094D\\u0915 \\u0935\\u093F\\u0928\\u094D\\u092F\\u093E\\u0938", "Parameter_Configuration": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u0935\\u093F\\u0928\\u094D\\u092F\\u093E\\u0938", "Follow_Up": "\\u0905\\u0928\\u0941\\u0935\\u0930\\u094D\\u0924\\u0940", "Medicine_Stock": "\\u0926\\u0935\\u093E\\u0908 \\u0915\\u093E \\u092D\\u0923\\u094D\\u0921\\u093E\\u0930", "Test_Fee_Configuration": "\\u091F\\u0947\\u0938\\u094D\\u091F \\u0936\\u0941\\u0932\\u094D\\u0915 \\u0935\\u093F\\u0928\\u094D\\u092F\\u093E\\u0938", "Upload_Complaints": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Download_Complaints_Template": "\\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F", "Upload": "\\u0905\\u092A\\u0932\\u094B\\u0921", "Close": "\\u092C\\u0902\\u0926 \\u0915\\u0930\\u0947", "Generate_Password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0915\\u0930\\u0947\\u0902", "Reset_Password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0938\\u094D\\u0925\\u093E\\u092A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Upload_Medicine": "\\u0926\\u0935\\u093E \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Download_Medicine_Template": "\\u0926\\u0935\\u093E \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Upload_Recommendations": "\\u0938\\u093F\\u092B\\u093E\\u0930\\u093F\\u0936\\u0947\\u0902 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Add_Logo": "\\u0932\\u094B\\u0917\\u094B \\u091C\\u094B\\u0921\\u093C\\u0947\\u0902", "Add_Hospital_Name": "\\u0905\\u0938\\u094D\\u092A\\u0924\\u093E\\u0932 \\u0915\\u093E \\u0928\\u093E\\u092E \\u091C\\u094B\\u0921\\u093C\\u0947\\u0902", "Follow-up_Discount": "\\u0905\\u0928\\u0941\\u0935\\u0930\\u094D\\u0924\\u0940 \\u091B\\u0942\\u091F", "Save": "\\u0930\\u0915\\u094D\\u0937\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Select": "\\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947", "Upload_Intervention": "\\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Download_Intervention_Template": "\\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Download_Recommondations_Template": "\\u0905\\u0928\\u0941\\u0936\\u0902\\u0938\\u093F\\u0924 \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Select_Doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Nurse": "\\u0928\\u0930\\u094D\\u0938", "Save_Discount": "\\u091B\\u0942\\u091F \\u0938\\u0902\\u091A\\u092F \\u0915\\u0930\\u0928\\u093E", "Upload_Medicine_Stock": "\\u0926\\u0935\\u093E\\u0908 \\u0915\\u093E \\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Upload_TestIntervention": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Generate_TestPrices_Template": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092E\\u0942\\u0932\\u094D\\u092F \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u092C\\u0928\\u093E\\u090F\\u0901", "Generate_Stock_Template": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0915\\u0930\\u0947\\u0902", "chief_complaints": "\\u092E\\u0941\\u0916\\u094D\\u092F \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902", "please_enter_chief_complaint": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092E\\u0941\\u0916\\u094D\\u092F \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "data_saved_successfully": "\\u0921\\u0947\\u091F\\u093E \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E!", "problem_while_saving_chief_complaint": "\\u092E\\u0941\\u0916\\u094D\\u092F \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0915\\u094B \\u0938\\u0939\\u0947\\u091C\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Initializing": "\\u0936\\u0941\\u0930\\u0941 \\u0915\\u0930 \\u0930\\u0939\\u093E \\u0939\\u0948.......", "PleaseWait": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902.....", "Tray_with_cassette_is_inserted_properly": "\\u0915\\u0948\\u0938\\u0947\\u091F \\u0915\\u0947 \\u0938\\u093E\\u0925 \\u091F\\u094D\\u0930\\u0947 \\u0915\\u094B \\u0920\\u0940\\u0915 \\u0938\\u0947 \\u0921\\u093E\\u0932\\u093E \\u091C\\u093E\\u0924\\u093E \\u0939\\u0948\\u0964", "Makesure_device_connected_and_switched_ON": "\\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0939\\u0941\\u0906 \\u0939\\u0948 \\u0914\\u0930 \\u091A\\u093E\\u0932\\u0942 \\u0939\\u0948\\u0964", "error_while_taking_BP": "\\u092C\\u0940\\u092A\\u0940 \\u0932\\u0947\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F\\u0964", "Measurement_Error": "\\u092E\\u093E\\u092A \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Pulse_rate_Error": "\\u092A\\u0932\\u094D\\u0938 \\u0930\\u0947\\u091F  \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Too_low_BP_(beyond_range)": "\\u092C\\u0939\\u0941\\u0924 \\u0915\\u092E \\u092C\\u0940\\u092A\\u0940 (\\u0938\\u0940\\u092E\\u093E \\u0938\\u0947 \\u092A\\u0930\\u0947)", "Very_high_BP_(beyond_range)": "\\u092C\\u0939\\u0941\\u0924 \\u0909\\u091A\\u094D\\u091A \\u092C\\u0940\\u092A\\u0940 (\\u0938\\u0940\\u092E\\u093E \\u0938\\u0947 \\u092A\\u0930\\u0947)", "Overpressure_Error_The_cuff_pressure_exceeded_280mmHg": "\\u0926\\u092C\\u093E\\u0935 \\u092E\\u0947\\u0902 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F\\u0964 \\u0915\\u092B \\u0926\\u092C\\u093E\\u0935 280 \\u092E\\u093F\\u092E\\u0940 \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915 \\u0939\\u094B \\u0917\\u092F\\u093E", "Deflation_Error": "\\u092A\\u093F\\u091A\\u0915\\u093E\\u092F\\u0947 \\u091C\\u093E\\u0928\\u0947 \\u0915\\u0940 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Inflation_Error": "\\u092B\\u0941\\u0932\\u093E\\u090F \\u091C\\u093E\\u0928\\u0947 \\u0915\\u0940 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Cuff_is_not_connected": "\\u0915\\u092B \\u091C\\u0941\\u0921\\u093C\\u093E \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "Lead_not_connected": "\\u0932\\u0940\\u0921 \\u091C\\u0941\\u0921\\u093C\\u093E \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948\\u0964", "StethoScope": "\\u092A\\u0930\\u093F\\u0936\\u094D\\u0930\\u093E\\u0935\\u0915", "Error_while_saving_data": "\\u0921\\u0947\\u091F\\u093E \\u0938\\u0939\\u0947\\u091C\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F\\u0964", "Waiting_for_Sensor_to_Connect": "\\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0915\\u0940 \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0940 \\u091C\\u093E \\u0930\\u0939\\u0940 \\u0939\\u0948", "Otoscope": "\\u0913\\u091F\\u094B\\u0938\\u094D\\u0915\\u093E\\u092A", "Please_SwitchOn_StethoSensor_Device": "\\u0943\\u092A\\u092F\\u093E \\u0938\\u094D\\u091F\\u0947\\u0925\\u094B \\u0938\\u0947\\u0902\\u0938\\u0930 \\u091A\\u093E\\u0932\\u0942 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_take_proper_reading": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0932\\u0947\\u0902", "Enter_Valid_Glucose_percentage": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C \\u092A\\u094D\\u0930\\u0924\\u093F\\u0936\\u0924 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Gain_Setting": "\\u0932\\u093E\\u092D \\u0938\\u0947\\u091F\\u093F\\u0902\\u0917", "Patient_Monitor": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0928\\u093F\\u0917\\u0930\\u093E\\u0928\\u0940", "Tests": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Yes": "\\u0939\\u093E\\u0901", "No": "\\u0928\\u0939\\u0940\\u0902", "Dispensed": "\\u092C\\u093E\\u0902\\u091F\\u0928\\u093E", "Stock": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930", "Expiry": "\\u0938\\u092E\\u093E\\u092A\\u094D\\u0924\\u093F", "Batch": "\\u091C\\u0924\\u094D\\u0925\\u093E", "Serial_No": "\\u0905\\u0928\\u0941 \\u0915\\u094D\\u0930\\u092E\\u093E\\u0902\\u0915", "Stock_Information": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0915\\u0940 \\u091C\\u093E\\u0928\\u0915\\u093E\\u0930\\u0940", "Dose": "\\u0914\\u0937\\u0927\\u093F \\u0915\\u0940 \\u092E\\u093E\\u0924\\u094D\\u0930\\u093E", "No_Of_Days": "\\u0926\\u093F\\u0928\\u094B\\u0902 \\u0915\\u0940 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E", "Stock_updated_successfully": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u093F\\u092F\\u093E!", "Error_while_updating_stock": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F!", "Please_update_stock": "\\u0943\\u092A\\u092F\\u093E \\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0947\\u0902!", "freetext_entry_OR_Not_in_inventory": "\\u0928\\u093F", "only": "\\u0915\\u0947\\u0935\\u0932", "Interventions_uploaded_successfully": "\\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Complaints_uploaded_successfully": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u094B\\u0902 \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Recommendations_uploaded_successfully": "\\u0905\\u0928\\u0941\\u0936\\u0902\\u0938\\u093E\\u0913\\u0902 \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "There_was_an_error": "\\u090F\\u0915 \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F \\u0939\\u0941\\u0908", "Request_doesnot_contain_uploaddata": "\\u0905\\u0928\\u0941\\u0930\\u094B\\u0927 \\u092E\\u0947\\u0902 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0921\\u0947\\u091F\\u093E \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "TestIntervention_uploaded_successfully": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Medicines_uploaded_successfully": "\\u0926\\u0935\\u093E\\u0913\\u0902 \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Uploaded_excelformat_not_correct_duplicaterow_entry": "\\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u090F\\u0915\\u094D\\u0938\\u0947\\u0932 \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A \\u0938\\u0939\\u0940 \\u092F\\u093E \\u0921\\u0941\\u092A\\u094D\\u0932\\u093F\\u0915\\u0947\\u091F \\u092A\\u0902\\u0915\\u094D\\u0924\\u093F \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "Please_upload_file": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Reduce_Stock": "\\u0938\\u094D\\u091F\\u0949\\u0915 \\u0915\\u092E \\u0915\\u0930\\u0947\\u0902", "Add_Stock": "\\u0938\\u094D\\u091F\\u0949\\u0915 \\u092C\\u095D\\u093E\\u092F\\u0947", "No_data_available": "\\u0915\\u094B\\u0908 \\u0921\\u0947\\u091F\\u093E \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948 !", "Fee": "\\u0936\\u0941\\u0932\\u094D\\u0915", "Parameter_Price": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u092E\\u0942\\u0932\\u094D\\u092F", "Approve_Paid_Coupons": "\\u092A\\u0947\\u0921 \\u0915\\u0942\\u092A\\u0928 \\u0938\\u094D\\u0935\\u0940\\u0915\\u0943\\u0924 \\u0915\\u0930\\u0947\\u0902", "Available_Free_Coupons": "\\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u092E\\u0941\\u092B\\u094D\\u0924 \\u0915\\u0942\\u092A\\u0928", "Available_Paid_Coupons": "\\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0915\\u0942\\u092A\\u0928", "Requested_Paid_Coupons": "\\u0905\\u0928\\u0941\\u0930\\u094B\\u0927\\u093F\\u0924 \\u0915\\u0942\\u092A\\u0928", "Nurse_Username": "\\u0928\\u0930\\u094D\\u0938 \\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0928\\u093E\\u092E", "Select_District": "\\u091C\\u093F\\u0932\\u0947 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_State": "\\u0930\\u093E\\u091C\\u094D\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Country": "\\u0926\\u0947\\u0936 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Form": "\\u092B\\u0949\\u0930\\u094D\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Class": "\\u0915\\u0915\\u094D\\u0937\\u093E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Brand": "\\u092C\\u094D\\u0930\\u093E\\u0902\\u0921 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Your_followup_discount_added_successfully": "\\u0906\\u092A\\u0915\\u0940 \\u0905\\u0928\\u0941\\u0935\\u0930\\u094D\\u0924\\u0940 \\u091B\\u0942\\u091F \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u091C\\u094B\\u0921\\u093C\\u0940 \\u0917\\u0908\\u0964", "please_enter_followup_discount": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0905\\u0928\\u0941\\u0935\\u0930\\u094D\\u0924\\u0940 \\u091B\\u0942\\u091F \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "please_select_first_username": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0939\\u0932\\u0947 \\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0928\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "record_not_found": "\\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E", "device_is_not_reachable": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0939\\u0941\\u0902\\u091A \\u092F\\u094B\\u0917\\u094D\\u092F \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948", "Capture_finger_print": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0948\\u092A\\u094D\\u091A\\u0930 \\u0915\\u0930\\u0947\\u0902", "Connect": "\\u091C\\u0941\\u0921\\u093F\\u092F\\u0947", "Problem_generate_new_password": "\\u0928\\u092F\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u091C\\u0928\\u0930\\u0947\\u091F \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Select_DateFormat": "\\u0924\\u093F\\u0925\\u093F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Speciality": "\\u0935\\u093F\\u0936\\u0947\\u0937\\u0924\\u093E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Display_Date_Format": "\\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A \\u092A\\u094D\\u0930\\u0926\\u0930\\u094D\\u0936\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Download_Stock_Template": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F \\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "download_test_intervention_template": "\\u0921\\u093E\\u0909\\u0928\\u0932\\u094B\\u0921 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0939\\u0938\\u094D\\u0924\\u0915\\u094D\\u0937\\u0947\\u092A \\u091F\\u0947\\u092E\\u094D\\u092A\\u0932\\u0947\\u091F", "Not_Generated": "\\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0941\\u0906", "Generated_Successfully": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0909\\u0924\\u094D\\u092A\\u0928\\u094D\\u0928", "Available": "\\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0915\\u0942\\u092A\\u0928", "Out_of_stock": "\\u092D\\u0923\\u094D\\u0921\\u093E\\u0930 \\u0916\\u093C\\u0924\\u094D\\u092E", "ExpiryDate": "\\u0938\\u092E\\u093E\\u092A\\u094D\\u0924\\u093F \\u0924\\u093F\\u0925\\u093F", "Batch_Number": "\\u092C\\u0948\\u091A \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E", "Available_Stock": "\\u092E\\u094C\\u091C\\u0942\\u0926\\u093E \\u092D\\u0902\\u0921\\u093E\\u0930", "Medicine_Name": "\\u0926\\u0935\\u093E \\u0915\\u093E \\u0928\\u093E\\u092E", "Please_enter_followup_discount_in_percentage": "\\u0915\\u0943\\u092A\\u092F\\u093E% \\u092E\\u0947\\u0902 \\u091B\\u0942\\u091F \\u0915\\u093E \\u092A\\u093E\\u0932\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Nurse": "\\u0928\\u0930\\u094D\\u0938 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Upload_File_Size_lessthan_2Mb": "\\u0905\\u092A\\u0932\\u094B\\u0921 \\u092B\\u093C\\u093E\\u0907\\u0932 \\u0915\\u093E \\u0906\\u0915\\u093E\\u0930 2 \\u090F\\u092E\\u092C\\u0940 \\u0938\\u0947 \\u0915\\u092E \\u0939\\u094B\\u0928\\u093E \\u091A\\u093E\\u0939\\u093F\\u090F", "Test": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Select_LabName": "\\u0932\\u0948\\u092C \\u0928\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Disabled": "\\u0905\\u0915\\u094D\\u0937\\u092E \\u0915\\u0930\\u0947\\u0902", "Enabled": "\\u0938\\u0915\\u094D\\u0937\\u092E \\u0915\\u0930\\u0947\\u0902", "ACR_ManualEntry": "\\u090F\\u0938\\u0940\\u0906\\u0930 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Select_Result": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "select_recommendations": "\\u0938\\u093F\\u092B\\u093E\\u0930\\u093F\\u0936\\u094B\\u0902 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "UrineTest_Results_Saved_Successfully": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0915\\u0947 \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u0947 \\u0917\\u090F\\u0964", "Please_Initialize_again": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0936\\u0941\\u0930\\u0941\\u0906\\u0924 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_connect_the_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u092A\\u0915\\u0930\\u0923 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902 \\u0914\\u0930 \\u0938\\u0941\\u0928\\u093F\\u0936\\u094D\\u091A\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902 \\u0915\\u093F \\u0938\\u0902\\u0926\\u0930\\u094D\\u092D \\u092A\\u091F\\u094D\\u091F\\u0940 \\u0915\\u0947 \\u0938\\u093E\\u0925 \\u091F\\u094D\\u0930\\u0947 \\u0920\\u0940\\u0915 \\u0938\\u0947 \\u0921\\u093E\\u0932\\u0940 \\u0917\\u0908 \\u0939\\u0948\\u0964", "Urine_Note": "\\u0928\\u094B\\u091F", "Insert_tray_with_prepared_teststrip_in_device": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092E\\u0947\\u0902 \\u0924\\u0948\\u092F\\u093E\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u091F\\u094D\\u091F\\u0940 \\u0915\\u0947 \\u0938\\u093E\\u0925 \\u091F\\u094D\\u0930\\u0947 \\u0921\\u093E\\u0932\\u0947\\u0902\\u0964", "Place_teststrip_on_tray_proper_position": "\\u091F\\u094D\\u0930\\u0947 \\u092A\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u091F\\u094D\\u091F\\u0940 \\u0915\\u094B \\u0909\\u091A\\u093F\\u0924 \\u0938\\u094D\\u0925\\u093F\\u0924\\u093F \\u092E\\u0947\\u0902 \\u0930\\u0916\\u0947\\u0902", "Remove_excess_urine": "\\u0905\\u0924\\u093F\\u0930\\u093F\\u0915\\u094D\\u0924 \\u092E\\u0942\\u0924\\u094D\\u0930 \\u0939\\u091F\\u093E\\u090F", "Click_startbutton_when_removing_teststrip_from_urinesample": "\\u0909\\u0938 \\u0938\\u092E\\u092F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u092C\\u091F\\u0928 \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902 \\u091C\\u092C \\u0906\\u092A \\u092E\\u0942\\u0924\\u094D\\u0930 \\u0915\\u0947 \\u0928\\u092E\\u0942\\u0928\\u0947 \\u0938\\u0947 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u091F\\u094D\\u091F\\u0940 \\u0939\\u091F\\u093E\\u0924\\u0947 \\u0939\\u0948\\u0902", "Dip_teststrip_in_Urinesample": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u0915\\u0947 \\u0928\\u092E\\u0942\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0921\\u093F\\u092A \\u0938\\u094D\\u091F\\u094D\\u0930\\u093F\\u092A", "Computing_Result": "\\u0915\\u092E\\u094D\\u092A\\u094D\\u092F\\u0942\\u091F\\u093F\\u0902\\u0917 \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E", "HBA1C_ManualEntry": "HBA1C \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u090F\\u0902\\u091F\\u094D\\u0930\\u0940", "Please_SwitchOn_FetalSensor_Device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092D\\u094D\\u0930\\u0942\\u0923 \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "Spirometry": "\\u0938\\u094D\\u092A\\u093F\\u0930\\u094B\\u092E\\u0947\\u091F\\u094D\\u0930\\u0940", "Please_SwitchOn_SpiroSensor_Device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u094D\\u092A\\u093E\\u0907\\u0930\\u094B \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "SpiroMeter_ManualEntry": "\\u0938\\u094D\\u092A\\u093E\\u0907\\u0930\\u094B\\u092E\\u0940\\u091F\\u0930 \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u090F\\u0902\\u091F\\u094D\\u0930\\u0940", "LipidProfile": "\\u0932\\u093F\\u092A\\u093F\\u0921 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932", "Lipid_ManualEntry": "\\u0932\\u093F\\u092A\\u093F\\u0921 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "UrineTest_ManualEntry": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "RapidTest_ManualEntry": "\\u0930\\u0948\\u092A\\u093F\\u0921 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Glucose_ManualEntry": "\\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Please_SwitchOn_Spo2Sensor": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0932\\u094D\\u0938\\u094B\\u0915\\u094D\\u0938\\u093F\\u092E\\u0940\\u091F\\u0930 \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "BloodPressure_ManualEntry": "\\u092C\\u094D\\u0932\\u0921\\u092A\\u094D\\u0930\\u0947\\u0936\\u0930 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Please_SwitchOn_BPSensorDevice": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092C\\u094D\\u0932\\u0921 \\u092A\\u094D\\u0930\\u0947\\u0936\\u0930 \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_SwitchOn_ECGSensorDevice": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0907\\u0932\\u0947\\u0915\\u094D\\u091F\\u094D\\u0930\\u094B\\u0915\\u093E\\u0930\\u094D\\u0921\\u093F\\u092F\\u094B\\u0917\\u094D\\u0930\\u093E\\u092E \\u0938\\u0947\\u0902\\u0938\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "PulseOximeter_ManualEntry": "\\u092A\\u0932\\u094D\\u0938\\u0913\\u0938\\u0947\\u092E\\u0940\\u091F\\u0930 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Please_Switch_On_Temperature_Device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0924\\u093E\\u092A\\u092E\\u093E\\u0928 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902\\u0964", "Temperature_Manual_Entry": "\\u0924\\u093E\\u092A\\u092E\\u093E\\u0928 \\u0939\\u093E\\u0925 \\u0938\\u0947 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Failed_to_save_UrineTest_ManualEntrydata": "\\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u090F\\u0902\\u091F\\u094D\\u0930\\u0940 \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u092C\\u091A\\u093E\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0964", "Successfully_saved_Urine_Test_Manual_Entry_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u090F\\u0902\\u091F\\u094D\\u0930\\u0940 \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u092C\\u091A\\u093E\\u092F\\u093E\\u0964", "Please_proper_GLU_value_format": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 GLU \\u092E\\u093E\\u0928 \\u0938\\u094D\\u0935\\u0930\\u0942\\u092A \\u0926\\u0947\\u0902", "Please_proper_KET_value_format": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 KET \\u092E\\u093E\\u0928 \\u092B\\u093C\\u0949\\u0930\\u094D\\u092E\\u0947\\u091F \\u0915\\u0930\\u0947\\u0902", "Please_proper_BLO_value_format": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 \\u092C\\u0940\\u090F\\u0932\\u0913 \\u092E\\u0942\\u0932\\u094D\\u092F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A", "Please_proper_PRO_value_format": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 \\u092A\\u0940\\u0906\\u0930\\u0913 \\u092E\\u093E\\u0928 \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A", "Please_proper_LEU_value_format": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0909\\u091A\\u093F\\u0924 LEU \\u092E\\u093E\\u0928 \\u092A\\u094D\\u0930\\u093E\\u0930\\u0942\\u092A", "Please_select_NIT_test_result": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u094D\\u0930\\u0949\\u092A\\u0921\\u093E\\u0909\\u0928 \\u0938\\u0947 \\u090F\\u0928\\u0906\\u0908\\u091F\\u0940 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u093E \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Failed_to_save": "\\u092C\\u091A\\u093E\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932", "data": "\\u0921\\u0947\\u091F\\u093E\\u0964", "Successfully_saved": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0902\\u091A\\u093F\\u0924 \\u0915\\u0930 \\u0932\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Please_select_testresult_from_dropdown": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u094D\\u0930\\u0949\\u092A\\u0921\\u093E\\u0909\\u0928 \\u0938\\u0947 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u093E \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_select_TestType_from_dropdown": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u094D\\u0930\\u0949\\u092A\\u0921\\u093E\\u0909\\u0928 \\u0938\\u0947 \\u091F\\u0947\\u0938\\u094D\\u091F \\u091F\\u093E\\u0907\\u092A \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_wait_fetching_result_from_device": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902, \\u0939\\u092E \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0938\\u0947 \\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0932\\u093E \\u0930\\u0939\\u0947 \\u0939\\u0948\\u0902!", "Failed_to_save_Fetal_data": "\\u092D\\u094D\\u0930\\u0942\\u0923 \\u0915\\u0947 \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u0938\\u0939\\u0947\\u091C\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0935\\u093F\\u092B\\u0932\\u0964", "Successfully_saved_Fetal_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092D\\u094D\\u0930\\u0942\\u0923 \\u0921\\u0947\\u091F\\u093E \\u092C\\u091A\\u093E\\u092F\\u093E\\u0964", "Enter_Valid_HeartRate_Reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0939\\u0943\\u0926\\u092F \\u0926\\u0930 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Result_fetched_successfully": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092A\\u094D\\u0930\\u093E\\u092A\\u094D\\u0924 \\u0939\\u0941\\u0906!", "Error_while_saving_Hba1c_data": "Hba1c \\u0921\\u0947\\u091F\\u093E \\u0938\\u0939\\u0947\\u091C\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0924\\u094D\\u0930\\u0941\\u091F\\u093F", "Successfully_saved_Hba1c_data": "Hba1c \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E\\u0964", "Please_enter_hemoglobin_percentage": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0939\\u0940\\u092E\\u094B\\u0917\\u094D\\u0932\\u094B\\u092C\\u093F\\u0928% \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_Lipid_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0932\\u093F\\u092A\\u093F\\u0921 \\u0921\\u0947\\u091F\\u093E \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u0940\\u0924\\u0964", "Enter_Valid_Triglycerides_Cholestrol_reading": "\\u0935\\u0948\\u0927 \\u091F\\u094D\\u0930\\u093E\\u0907\\u0917\\u094D\\u0932\\u093F\\u0938\\u0930\\u093E\\u0907\\u0921\\u094D\\u0938 \\u0915\\u094B\\u0932\\u0947\\u0938\\u094D\\u091F\\u094D\\u0930\\u094B\\u0932 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_HDL_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u090F\\u091A\\u0921\\u0940\\u090F\\u0932 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_Total_Cholestrol_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0915\\u0941\\u0932 \\u0915\\u094B\\u0932\\u0947\\u0938\\u094D\\u091F\\u094D\\u0930\\u094B\\u0932 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_Spirometer_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u0940\\u0924 \\u0938\\u094D\\u092A\\u093E\\u0907\\u0930\\u094B\\u092E\\u0940\\u091F\\u0930 \\u0921\\u0947\\u091F\\u093E\\u0964", "Enter_Valid_TLC_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F TLC \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_TV_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u091F\\u0940\\u0935\\u0940 \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_PEF_rate": "\\u0935\\u0948\\u0927 \\u092A\\u0940\\u0908\\u090F\\u092B \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FEF75_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F FEF75 \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FEF50_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F FEF50 \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FEF25_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F FEF25 \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FEV_Percentage_rate": "\\u092E\\u093E\\u0928\\u094D\\u092F FEV \\u092A\\u094D\\u0930\\u0924\\u093F\\u0936\\u0924 \\u0926\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FEV1_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F FEV1 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_FVC_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F FVC \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_bloodpressure_data": "\\u0930\\u0915\\u094D\\u0924\\u091A\\u093E\\u092A \\u0915\\u0947 \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092C\\u091A\\u093E\\u092F\\u093E\\u0964", "Enter_Valid_diastolic_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0921\\u093E\\u092F\\u0938\\u094D\\u091F\\u094B\\u0932\\u093F\\u0915 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_systolic_reading": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0938\\u093F\\u0938\\u094D\\u091F\\u094B\\u0932\\u093F\\u0915 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_ACR_data": "ACR \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E\\u0964", "Triglycerides": "\\u091F\\u094D\\u0930\\u093E\\u0907\\u0917\\u094D\\u0932\\u093F\\u0938\\u0930\\u093E\\u0907\\u0921\\u094D\\u0938", "Successfully_saved_Temperature_data": "\\u0924\\u093E\\u092A\\u092E\\u093E\\u0928 \\u0921\\u0947\\u091F\\u093E \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0939\\u0947\\u091C\\u093E \\u0917\\u092F\\u093E\\u0964", "Enter_Valid_entryof_fahrenheit": "\\u092B\\u093C\\u093E\\u0930\\u0947\\u0928\\u0939\\u093E\\u0907\\u091F \\u092E\\u0947\\u0902 \\u0935\\u0948\\u0927 \\u092A\\u094D\\u0930\\u0935\\u0947\\u0936 \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_Spo2_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u093F\\u0924 \\u092A\\u0932\\u094D\\u0938\\u094B\\u0915\\u094D\\u0938\\u093F\\u092E\\u0940\\u091F\\u0930 \\u0921\\u0947\\u091F\\u093E\\u0964", "Values_cannot_be_empty": "\\u092E\\u093E\\u0928 \\u0930\\u093F\\u0915\\u094D\\u0924 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u094B \\u0938\\u0915\\u0924\\u0947\\u0964", "Enter_Valid_pulse": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u092A\\u0932\\u094D\\u0938 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Enter_Valid_oxygen_percentage": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0911\\u0915\\u094D\\u0938\\u0940\\u091C\\u0928 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0936\\u0924 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Successfully_saved_Glucose_data": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C \\u0921\\u0947\\u091F\\u093E \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u0940\\u0924\\u0964", "Enter_Valid_Glucose_Value": "\\u092E\\u093E\\u0928\\u094D\\u092F \\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C \\u092E\\u093E\\u0928 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902\\u0964", "Fields_cannot_be_empty": "\\u092B\\u093C\\u0940\\u0932\\u094D\\u0921 \\u0915\\u0947\\u0935\\u0932 \\u0921\\u0949\\u091F \\u092F\\u093E \\u0930\\u093F\\u0915\\u094D\\u0924 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u094B \\u0938\\u0915\\u0924\\u0940 \\u0939\\u0948\\u0902.", "Please_select_test": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902!", "Please_take_reading_again": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0932\\u0947\\u0902", "After_beepsound_please_Waitfor_2sec_tostart_reading": "\\u092C\\u0940\\u092A \\u0938\\u093E\\u0909\\u0902\\u0921 \\u0915\\u0947 \\u092C\\u093E\\u0926 \\u092A\\u0922\\u0928\\u093E \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F 2 \\u0938\\u0947\\u0915\\u0902\\u0921 \\u0924\\u0915 \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902", "Reading_improper_please_take_again": "\\u0930\\u0940\\u0921\\u093F\\u0902\\u0917 \\u0920\\u0940\\u0915 \\u0938\\u0947 \\u0928\\u0939\\u0940\\u0902 \\u0932\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093F\\u0930 \\u0938\\u0947 \\u0932\\u0947\\u0902\\u0964", "no_manual_entry_param": "\\u0915\\u094B\\u0908 \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F \\u0928\\u0939\\u0940\\u0902", "HDL_Cholesterol": "\\u090F\\u091A \\u0921\\u0940 \\u090F\\u0932 \\u0915\\u094B\\u0932\\u0947\\u0938\\u094D\\u091F\\u094D\\u0930\\u0949\\u0932", "Total_Cholesterol": "\\u0915\\u0941\\u0932 \\u0915\\u094B\\u0932\\u0947\\u0938\\u094D\\u091F\\u094D\\u0930\\u0949\\u0932", "GLUCOSE": "\\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C", "BILIRUBIN": "\\u092C\\u093F\\u0932\\u0940\\u0930\\u0941\\u092C\\u093F\\u0928", "SPECIFIC_GRAVITY": "\\u0935\\u093F\\u0936\\u093F\\u0937\\u094D\\u091F \\u0917\\u0941\\u0930\\u0941\\u0924\\u094D\\u0935", "BLOOD": "\\u0930\\u0915\\u094D\\u0924", "PROTEIN": "\\u092A\\u094D\\u0930\\u094B\\u091F\\u0940\\u0928", "UROBILINOGEN": "\\u092F\\u0942\\u0930\\u094B\\u092C\\u093E\\u092F\\u0932\\u093F\\u0928\\u094B\\u091C\\u0947\\u0928", "NITRITE": "\\u0928\\u093E\\u0907\\u091F\\u094D\\u0930\\u093E\\u091F", "LEUKOCYTES": "\\u0932\\u094D\\u092F\\u0942\\u0915\\u094B\\u0938\\u093E\\u0907\\u091F\\u094D\\u0938", "ReferenceStrip": "\\u0938\\u0902\\u0926\\u0930\\u094D\\u092D \\u092A\\u091F\\u094D\\u091F\\u0940", "TestStrip": "\\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923 \\u092A\\u091F\\u094D\\u091F\\u093F\\u0915\\u093E", "Result": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E", "Processing": "\\u092A\\u094D\\u0930\\u0938\\u0902\\u0938\\u094D\\u0915\\u0930\\u0923", "Initialize": "\\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0915\\u0930\\u0947\\u0902", "Click_the_Initialize": "\\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Check_referencestrip_in_tray": "\\u091F\\u094D\\u0930\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u0902\\u0926\\u0930\\u094D\\u092D \\u092A\\u091F\\u094D\\u091F\\u0940 \\u0915\\u0940 \\u0909\\u092A\\u0938\\u094D\\u0925\\u093F\\u0924\\u093F \\u0915\\u0940 \\u091C\\u093E\\u0902\\u091A \\u0915\\u0930\\u0947\\u0902", "Negative": "\\u0928\\u0915\\u093E\\u0930\\u093E\\u0924\\u094D\\u092E\\u0915", "Positive": "\\u0938\\u0915\\u093E\\u0930\\u093E\\u0924\\u094D\\u092E\\u0915", "Click_the_Start_Test_button": "\\u0938\\u094D\\u091F\\u093E\\u0930\\u094D\\u091F \\u091F\\u0947\\u0938\\u094D\\u091F \\u092C\\u091F\\u0928 \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Select_Test_from_dropdown": "\\u0921\\u094D\\u0930\\u0949\\u092A\\u0921\\u093E\\u0909\\u0928 \\u0938\\u0947 \\u091F\\u0947\\u0938\\u094D\\u091F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Switch_on_device": "\\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902", "Insert_tray_in_optical_device": "\\u0911\\u092A\\u094D\\u091F\\u093F\\u0915\\u0932 \\u0930\\u0940\\u0921\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u092E\\u0947\\u0902 \\u091F\\u094D\\u0930\\u0947 \\u0921\\u093E\\u0932\\u0947\\u0902", "Connect_optical_reader": "USB \\u0915\\u0947\\u092C\\u0932 \\u0915\\u093E \\u0909\\u092A\\u092F\\u094B\\u0917 \\u0915\\u0930\\u0915\\u0947 \\u0911\\u092A\\u094D\\u091F\\u093F\\u0915\\u0932 \\u0930\\u0940\\u0921\\u0930 \\u0921\\u093F\\u0935\\u093E\\u0907\\u0938 \\u0915\\u094B \\u0915\\u0902\\u092A\\u094D\\u092F\\u0942\\u091F\\u0930 \\u0938\\u0947 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902", "Please_follow_steps": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0928\\u0940\\u091A\\u0947 \\u0926\\u093F\\u090F \\u0917\\u090F \\u091A\\u0930\\u0923\\u094B\\u0902 \\u0915\\u093E \\u092A\\u093E\\u0932\\u0928 \\u0915\\u0930\\u0947\\u0902", "AmplitudeBar": "\\u0906\\u092F\\u093E\\u092E \\u092C\\u093E\\u0930", "Note": "\\u0928\\u094B\\u091F", "Remedi2": {"0_not_running_refresh_page": "\\u0930\\u0947\\u092E\\u0947\\u0921\\u0940 2.0 \\u0906\\u0902\\u0924\\u0930\\u093F\\u0915 \\u0918\\u091F\\u0915 \\u0928\\u0939\\u0940\\u0902 \\u091A\\u0932 \\u0930\\u0939\\u093E \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0943\\u0937\\u094D\\u0920 \\u0915\\u094B \\u0930\\u093F\\u092B\\u094D\\u0930\\u0947\\u0936 \\u0915\\u0930\\u0947\\u0902"}, "SaveResults": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0938\\u0939\\u0947\\u091C\\u0947\\u0902", "Start": "\\u0936\\u0941\\u0930\\u0941", "StartTest": "\\u091F\\u0947\\u0938\\u094D\\u091F \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0947\\u0902", "HeartRate": "\\u0939\\u0943\\u0926\\u092F \\u0917\\u0924\\u093F", "Record": "\\u0905\\u092D\\u093F\\u0932\\u0947\\u0916", "Discard": "\\u091B\\u094B\\u0921\\u093C\\u0928\\u093E", "Next_Reading": "\\u0905\\u0917\\u0932\\u093E \\u092A\\u0920\\u0928", "Consultation_unmarked_as_followup": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0905\\u0928\\u0941\\u0935\\u0930\\u094D\\u0924\\u0940 \\u0915\\u0947 \\u0930\\u0942\\u092A \\u092E\\u0947\\u0902 \\u0905\\u091A\\u093F\\u0939\\u094D\\u0928\\u093F\\u0924 \\u0939\\u0948\\u0964", "Consultation_marked_as_followup": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0915\\u0947 \\u0930\\u0942\\u092A \\u092E\\u0947\\u0902 \\u091A\\u093F\\u0939\\u094D\\u0928\\u093F\\u0924\\u0964", "Blood_And_Urine_Tests": "\\u0930\\u0915\\u094D\\u0924 \\u0914\\u0930 \\u092E\\u0942\\u0924\\u094D\\u0930 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u0923", "Foetal_Care": "\\u092D\\u094D\\u0930\\u0942\\u0923 \\u0915\\u0940 \\u0926\\u0947\\u0916\\u092D\\u093E\\u0932", "ACR_Test": "\\u090F\\u0938\\u0940\\u0906\\u0930 \\u091F\\u0947\\u0938\\u094D\\u091F", "HbA1c": "\\u090F\\u091A\\u092C\\u0940\\u090F 1 \\u0938\\u0940", "HbA1C": "\\u090F\\u091A\\u092C\\u0940\\u090F 1 \\u0938\\u0940", "Lipid_Profile": "\\u0932\\u093F\\u092A\\u093F\\u0921 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932", "Physiology_Tests": "\\u092B\\u093F\\u091C\\u093F\\u092F\\u094B\\u0932\\u0949\\u091C\\u0940 \\u091F\\u0947\\u0938\\u094D\\u091F", "Ok": "\\u0920\\u0940\\u0915", "Problem_in_Changing_Password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u092C\\u0926\\u0932\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E\\u0964", "Old_Password_is_incorrect": "\\u092A\\u0941\\u0930\\u093E\\u0928\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0917\\u0932\\u0924 \\u0939\\u0948\\u0964", "Password_has_been_changed_successfully": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092C\\u0926\\u0932 \\u0926\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948\\u0964", "Please_check_the_terms_and_conditions": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0928\\u093F\\u092F\\u092E \\u0914\\u0930 \\u0936\\u0930\\u094D\\u0924\\u0947\\u0902 \\u091C\\u093E\\u0902\\u091A\\u0947\\u0902\\u0964", "terms_and_conditions": "\\u0928\\u093F\\u092F\\u092E \\u0914\\u0930 \\u0936\\u0930\\u094D\\u0924\\u0947\\u0902\\u0964", "I_agree_with": "\\u092E\\u0948\\u0902 \\u0938\\u0939\\u092E\\u0924 \\u0939\\u0942\\u0902", "Patient_already_in_consultation_with_some_other_user": "\\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u0915\\u0941\\u091B \\u0905\\u0928\\u094D\\u092F \\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0915\\u0947 \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0938\\u0947 \\u0930\\u094B\\u0917\\u0940", "edit": "\\u0938\\u0902\\u092A\\u093E\\u0926\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Search_By_Finger_Print": "\\u092B\\u093F\\u0902\\u0917\\u0930 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0938\\u0947 \\u0916\\u094B\\u091C\\u0947\\u0902", "Mail_Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u092E\\u0947\\u0932 \\u0915\\u0930\\u0947\\u0902 \\u092F\\u093E \\u0921\\u093E\\u0915 \\u0938\\u0947 \\u092D\\u0947\\u091C\\u0947\\u0902", "Thermal_Printer": "\\u0925\\u0930\\u094D\\u092E\\u0932 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F\\u0930", "Print_Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u091B\\u093E\\u092A\\u0947", "Medicines_not_precribed_in_this_consultation": "\\u0907\\u0938 \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u092E\\u0947\\u0902 \\u0926\\u0935\\u093E\\u090F\\u0902 \\u0928\\u0939\\u0940\\u0902 \\u0926\\u0940 \\u0917\\u0908 \\u0939\\u0948\\u0902!", "Select_User": "\\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Enter_Patient_Id": "\\u0930\\u094B\\u0917\\u0940 \\u0906\\u0908\\u0921\\u0940 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Slots_have_been_added_successfully": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u091C\\u094B\\u0921\\u093C\\u0947 \\u0917\\u090F \\u0939\\u0948\\u0902\\u0964", "Local_Consultation": "\\u0938\\u094D\\u0925\\u093E\\u0928\\u0940\\u092F \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936", "Remote_Consultation": "\\u0926\\u0942\\u0930\\u0938\\u094D\\u0925 \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936", "Review_Cases": "\\u092E\\u093E\\u092E\\u0932\\u094B\\u0902 \\u0915\\u0940 \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930\\u0947\\u0902", "Pharmacy_Configuration": "\\u092B\\u093E\\u0930\\u094D\\u092E\\u0947\\u0938\\u0940 \\u0915\\u0949\\u0928\\u094D\\u092B\\u093C\\u093F\\u0917\\u0930\\u0947\\u0936\\u0928", "Pharmacy": "\\u092B\\u093E\\u0930\\u094D\\u092E\\u0947\\u0938\\u0940", "Please_Enter_Quantity": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092E\\u093E\\u0924\\u094D\\u0930\\u093E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Quantity": "\\u092E\\u093E\\u0924\\u094D\\u0930\\u093E", "ProjectId": "\\u092A\\u094D\\u0930\\u094B\\u091C\\u0947\\u0915\\u094D\\u091F \\u0906\\u0908\\u0921\\u0940", "Username": "\\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0928\\u093E\\u092E", "Password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921", "Login": "\\u0932\\u0949\\u0917 \\u0907\\u0928 \\u0915\\u0930\\u0947\\u0902", "Neurosynaptic": "\\u0915\\u0949\\u092A\\u0940\\u0930\\u093E\\u0907\\u091F", "nurseLogin": "\\u0928\\u0930\\u094D\\u0938 \\u0932\\u0949\\u0917\\u093F\\u0928", "Hello": "\\u0928\\u092E\\u0938\\u094D\\u0924\\u0947", "Make_Appointments": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u092C\\u0928\\u093E\\u090F\\u0902", "Register_New_Patient": "\\u0928\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0915\\u0930\\u0947\\u0902", "Or_Fix_Appointment": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u092C\\u0941\\u0915 \\u0915\\u0930\\u0947\\u0902", "Choose_speciality": "\\u0935\\u093F\\u0936\\u0947\\u0937\\u0924\\u093E \\u091A\\u0941\\u0928\\u0947\\u0902", "Fee_Range": "\\u0936\\u0941\\u0932\\u094D\\u0915 \\u0938\\u0940\\u092E\\u093E", "Time_Range": "\\u0938\\u092E\\u092F \\u0938\\u0940\\u092E\\u093E", "Find_Doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Diagnostic_or_Patient_Records": "\\u0930\\u094B\\u0917\\u0940 \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921", "Or_Search_Patient": "\\u0909\\u0928\\u094D\\u0928\\u0924 \\u0916\\u094B\\u091C", "Or_Register_Patient": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u094B \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0915\\u0930\\u0947\\u0902", "Go_For_Diagnostic": "\\u0921\\u093E\\u092F\\u0917\\u094D\\u0928\\u094B\\u0938\\u094D\\u091F\\u093F\\u0915\\u094D\\u0938 \\u092A\\u0930 \\u091C\\u093E\\u090F\\u0902", "Go_For_Print_Patient_ID_Card": "\\u0930\\u094B\\u0917\\u0940 \\u0906\\u0908\\u0921\\u0940 \\u0915\\u093E\\u0930\\u094D\\u0921 \\u092A\\u094D\\u0930\\u093F\\u0902\\u091F \\u0915\\u0930\\u0947\\u0902", "View_Patient_Records": "\\u0930\\u094B\\u0917\\u0940 \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921\\u094D\\u0938", "Show_Appointments": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0926\\u093F\\u0916\\u093E\\u090F\\u0902", "Your_Appointments": "\\u0906\\u092A\\u0915\\u0940 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F", "Patient_Image": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0924\\u0938\\u094D\\u0935\\u0940\\u0930", "Patient_Name": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u093E \\u0928\\u093E\\u092E", "Start_Time": "\\u0938\\u092E\\u092F \\u0936\\u0941\\u0930\\u0942", "End_Time": "\\u0905\\u0902\\u0924\\u093F\\u092E \\u0938\\u092E\\u092F", "Appointment_Date": "\\u092E\\u093F\\u0932\\u0928\\u0947 \\u0915\\u0940 \\u0924\\u093E\\u0930\\u0940\\u0916", "Nurse_Name": "\\u0928\\u0930\\u094D\\u0938 \\u0915\\u093E \\u0928\\u093E\\u092E", "Status": "\\u0939\\u0948\\u0938\\u093F\\u092F\\u0924", "Action": "\\u0915\\u093E\\u0930\\u094D\\u092F", "Parameter_Manual_Entry_Status": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u092A\\u094D\\u0930\\u0935\\u093F\\u0937\\u094D\\u091F\\u093F", "Please_Provide_Patient_Id_first": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0906\\u0908\\u0921\\u0940 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_Provide__Valid_Patient_Id": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092E\\u093E\\u0928\\u094D\\u092F \\u0930\\u094B\\u0917\\u0940 \\u0906\\u0908\\u0921\\u0940 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902.", "Patient_is_not_registered": "\\u0930\\u094B\\u0917\\u0940 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u091C\\u093E\\u0902\\u091A\\u0947\\u0902\\u0964", "No_Patient_Found": "\\u0915\\u094B\\u0908 \\u0930\\u094B\\u0917\\u0940 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E", "No_Appointment_Found": "\\u0915\\u094B\\u0908 \\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E", "Active": "\\u0938\\u0915\\u094D\\u0930\\u093F\\u092F", "Cancelled": "\\u0930\\u0926\\u094D\\u0926", "Completed": "\\u092A\\u0942\\u0930\\u094D\\u0923", "Both_Active_and_Cancelled": "\\u0938\\u0915\\u094D\\u0930\\u093F\\u092F \\u0914\\u0930 \\u0930\\u0926\\u094D\\u0926 \\u0926\\u094B\\u0928\\u094B\\u0902", "Suspended": "\\u092C\\u0930\\u094D\\u0916\\u093E\\u0938\\u094D\\u0924 \\u0915\\u0930 \\u0926\\u093F\\u092F\\u093E", "All": "\\u0938\\u092C", "Pending": "\\u0930\\u0942\\u0915\\u093E \\u0939\\u0941\\u0906", "Reviewed": "\\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093F\\u0924", "Search_Result": "\\u092A\\u0930\\u093F\\u0923\\u093E\\u092E \\u0916\\u094B\\u091C\\u0947\\u0902", "Id": "\\u0906\\u0908\\u0921\\u0940", "Gender": "\\u0932\\u093F\\u0902\\u0917", "Date_of_Birth": "\\u091C\\u0928\\u094D\\u092E \\u0915\\u0940 \\u0924\\u093E\\u0930\\u0940\\u0916", "Search_Doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0916\\u094B\\u091C\\u0947\\u0902", "Fix_Appointment": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u092C\\u0941\\u0915 \\u0915\\u0930\\u0947\\u0902", "Book": "\\u092C\\u0941\\u0915", "Doctor_Name": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u093E \\u0928\\u093E\\u092E", "Degree": "\\u0921\\u093F\\u0917\\u094D\\u0930\\u0940", "Available_Slots": "\\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0938\\u094D\\u0932\\u0949\\u091F\\u094D\\u0938", "From_Time": "\\u0938\\u092E\\u092F \\u0938\\u0947", "To_Time": "\\u0938\\u092E\\u092F \\u092A\\u0930", "Cancel_the_slots": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930\\u0947\\u0902", "Book_the_slots": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u092C\\u0941\\u0915 \\u0915\\u0930\\u0947\\u0902", "Please_Select_Doctor_Name_First": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u093E \\u0928\\u093E\\u092E \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "Please_Select_Current_Date_First": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0935\\u0930\\u094D\\u0924\\u092E\\u093E\\u0928 \\u0924\\u093F\\u0925\\u093F \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "You_cannot_select_the_date_less_than_current_date": "\\u0906\\u092A \\u0935\\u0930\\u094D\\u0924\\u092E\\u093E\\u0928 \\u0924\\u093F\\u0925\\u093F \\u0938\\u0947 \\u0915\\u092E \\u0924\\u093E\\u0930\\u0940\\u0916 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947", "Please_Choose_slot_first": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0939\\u0932\\u0947 \\u0938\\u094D\\u0932\\u0949\\u091F \\u091A\\u0941\\u0928\\u0947\\u0902", "Slot_has_already_been_taken_by_someone_else": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u0915\\u093F\\u0938\\u0940 \\u0914\\u0930 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0932\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "No_Slot_Available": "\\u091A\\u092F\\u0928\\u093F\\u0924 \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0938\\u094D\\u0932\\u0949\\u091F \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u0948\\u0902", "Something_went_wrong_Please_retry": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E \\u0939\\u0948 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902", "something_went_to_wrong_please_relogin": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0932\\u0949\\u0917\\u093F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Problem_in_starting_local_consultation": "\\u0938\\u094D\\u0925\\u093E\\u0928\\u0940\\u092F \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "You_cannot_start_cancelled_appointment": "\\u0906\\u092A \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0940 \\u0917\\u0908 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0936\\u0941\\u0930\\u0942 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947", "OK": "\\u0920\\u0940\\u0915 \\u0939\\u0948", "search_Patient": "\\u0930\\u094B\\u0917\\u0940 \\u0916\\u094B\\u091C\\u0947\\u0902", "Register_By_You": "\\u0906\\u092A \\u0915\\u0947 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0930\\u091C\\u093F\\u0938\\u094D\\u091F\\u0930", "Register_By_All": "\\u0938\\u092D\\u0940 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0930\\u091C\\u093F\\u0938\\u094D\\u091F\\u0930 \\u0915\\u0930\\u0947\\u0902", "From_Date": "\\u0924\\u093E\\u0930\\u0940\\u0916 \\u0938\\u0947", "Till_Date": "\\u0924\\u093E\\u0930\\u0940\\u0916 \\u0924\\u0915", "patient_first_Name": "\\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E", "patient_Last_Name": "\\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E", "DOB": "\\u091C\\u0928\\u094D\\u092E \\u0915\\u0940 \\u0924\\u093E\\u0930\\u0940\\u0916", "Male": "\\u092A\\u0941\\u0930\\u0941\\u0937", "Female": "\\u092E\\u0939\\u093F\\u0932\\u093E", "Other": "\\u091F\\u094D\\u0930\\u093E\\u0902\\u0938\\u091C\\u0947\\u0902\\u0921\\u0930", "Age": "\\u0906\\u092F\\u0941", "Address": "\\u092A\\u0924\\u093E", "Search": "\\u0916\\u094B\\u091C", "Additional_Information": "\\u0905\\u0924\\u093F\\u0930\\u093F\\u0915\\u094D\\u0924 \\u091C\\u093E\\u0928\\u0915\\u093E\\u0930\\u0940", "Please_Provide_Patient_First_Name_to_Search": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0916\\u094B\\u091C \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0930\\u094B\\u0917\\u0940 \\u0915\\u093E \\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Mobile_contact_number": "\\u092E\\u094B\\u092C\\u093E\\u0907\\u0932 / \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0928\\u0902\\u092C\\u0930", "Married": "\\u0935\\u093F\\u0935\\u093E\\u0939\\u093F\\u0924", "Unmarried": "\\u0905\\u0935\\u093F\\u0935\\u093E\\u0939\\u093F\\u0924", "Hieght": "\\u090A\\u0902\\u091A\\u093E\\u0908", "Weight": "\\u0935\\u091C\\u0928", "Head_of_Household": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F", "Email": "\\u0908\\u092E\\u0947\\u0932", "Head_of_HouseHold_first_name": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F \\u0915\\u093E \\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E", "Head_of_HouseHold_Last_name": "\\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F \\u0915\\u093E \\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E", "UID": "\\u092F\\u0942\\u0906\\u0908\\u0921\\u0940", "Past_Records": "\\u092A\\u093F\\u091B\\u0932\\u0947 \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921", "Diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928", "Update_Patient": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u094B \\u0905\\u092A\\u0921\\u0947\\u091F \\u0915\\u0930\\u0947\\u0902", "Register_Patient": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u094B \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0915\\u0930\\u0947\\u0902", "Select_Block": "\\u092C\\u094D\\u0932\\u0949\\u0915 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Select_Village": "\\u0917\\u093E\\u0902\\u0935 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Upload_Patient_Image": "\\u0915\\u0948\\u092E\\u0930\\u0947 \\u0926\\u094D\\u0935\\u093E\\u0930\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0924\\u0938\\u094D\\u0935\\u0940\\u0930 \\u0915\\u0948\\u092A\\u094D\\u091A\\u0930 \\u0915\\u0930\\u0947\\u0902", "Take_another": "\\u090F\\u0915 \\u0914\\u0930 \\u0932\\u094B", "Please_Select_Country_First": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0939\\u0932\\u0947 \\u0926\\u0947\\u0936 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_Select_State_First": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u093E\\u091C\\u094D\\u092F \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_Select_District_First": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u091C\\u093F\\u0932\\u0947 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Please_Select_location_in_order": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0915\\u094D\\u0930\\u092E \\u092E\\u0947\\u0902 \\u0938\\u094D\\u0925\\u093E\\u0928 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Upload_Reports": "\\u0926\\u0938\\u094D\\u0924\\u093E\\u0935\\u0947\\u091C\\u093C / \\u091B\\u0935\\u093F \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Upload_from_computer": "\\u091B\\u0935\\u093F \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0915\\u0902\\u092A\\u094D\\u092F\\u0942\\u091F\\u0930 \\u092C\\u094D\\u0930\\u093E\\u0909\\u091C\\u093C \\u0915\\u0930\\u0947\\u0902", "Take_live_photo": "\\u0915\\u0948\\u092E\\u0930\\u0947 \\u0938\\u0947 \\u0924\\u0938\\u094D\\u0935\\u0940\\u0930 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Image_Type": "\\u091B\\u0935\\u093F \\u092A\\u094D\\u0930\\u0915\\u093E\\u0930", "Document_Type": "\\u0926\\u0938\\u094D\\u0924\\u093E\\u0935\\u0947\\u091C\\u093C \\u0915\\u093E \\u092A\\u094D\\u0930\\u0915\\u093E\\u0930", "Take_Another": "\\u090F\\u0915 \\u0914\\u0930 \\u0932\\u094B", "Register1": "\\u0930\\u091C\\u093F\\u0938\\u094D\\u091F\\u0930", "please_share_camera_first_and_then_take_photo": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092B\\u093C\\u094B\\u091F\\u094B \\u0932\\u0947\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0915\\u0948\\u092E\\u0930\\u093E \\u0938\\u093E\\u091D\\u093E \\u0915\\u0930\\u0947\\u0902\\u0964", "please_Browse_record_from_computer_OR_take_from_camera": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u091B\\u0935\\u093F \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0915\\u0902\\u092A\\u094D\\u092F\\u0942\\u091F\\u0930 \\u092C\\u094D\\u0930\\u093E\\u0909\\u091C\\u093C \\u0915\\u0930\\u0947\\u0902 \\u092F\\u093E \\u0915\\u0948\\u092E\\u0930\\u0947 \\u0915\\u093E \\u0909\\u092A\\u092F\\u094B\\u0917 \\u0915\\u0930\\u0915\\u0947 \\u090F\\u0915 \\u091B\\u0935\\u093F \\u0932\\u0947\\u0902\\u0964", "Please_Provide_Patient_Last_Name": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_Provide_Patient_First_Name": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0915\\u093E \\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_Provide_Patient_Age": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0906\\u092F\\u0941 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_Provide_Patient_Gender": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u094B\\u0917\\u0940 \\u0932\\u093F\\u0902\\u0917 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "Mobile_Number_Is_Invalid": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092E\\u093E\\u0928\\u094D\\u092F \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0928\\u0902\\u092C\\u0930 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902\\u0964", "Please_Choose_image_Type_for_record": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u091B\\u0935\\u093F \\u092A\\u094D\\u0930\\u0915\\u093E\\u0930 \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "Patient_is_registered_successfully_patient_Id": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u094B \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0943\\u0924 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948\\u0964 \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u0906\\u0908\\u0921\\u0940 \\u0939\\u0948", "Patient_registration_is_failed": "\\u0930\\u094B\\u0917\\u0940 \\u092A\\u0902\\u091C\\u0940\\u0915\\u0930\\u0923 \\u0935\\u093F\\u092B\\u0932 \\u0939\\u0948", "Please_Provide_valid_DOB": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0935\\u0948\\u0927 DOB \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902", "Invalid_Patient_First_Name": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u092A\\u094D\\u0930\\u0925\\u092E \\u0928\\u093E\\u092E", "Invalid_Patient_Last_Name": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E", "Invalid_Contact_First_Name": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u092A\\u0939\\u0932\\u093E \\u0928\\u093E\\u092E", "Invalid_Contact_Last_Name": "\\u0905\\u092E\\u093E\\u0928\\u094D\\u092F \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0905\\u0902\\u0924\\u093F\\u092E \\u0928\\u093E\\u092E", "Past_Reports": "\\u0935\\u093F\\u0917\\u0924 \\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F\\u0947\\u0902", "New_Records": "\\u0928\\u090F \\u0930\\u093F\\u0915\\u0949\\u0930\\u094D\\u0921", "View_Consultations": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0926\\u0947\\u0916\\u0947\\u0902", "Send_To_Doctor_for_Review": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u0947 \\u092A\\u093E\\u0938 \\u0938\\u092E\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0947 \\u0932\\u093F\\u090F \\u092D\\u0947\\u091C\\u0947\\u0902", "Save_Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u0938\\u0939\\u0947\\u091C\\u0947\\u0902", "Comment": "\\u091F\\u093F\\u092A\\u094D\\u092A\\u0923\\u0940", "Save_Comment": "\\u091F\\u093F\\u092A\\u094D\\u092A\\u0923\\u0940 \\u0938\\u0939\\u0947\\u091C\\u0947\\u0902", "Report_uploaded_successfully": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930 \\u0926\\u0940 \\u0917\\u0908 \\u0939\\u0948\\u0964", "Report_upload_failed": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0935\\u093F\\u092B\\u0932", "Cant_find_slot_please_try_again": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E, \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902", "Cant_find_patient_please_try_again": "\\u0930\\u094B\\u0917\\u0940 \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E, \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902", "Cant_find_doctor_please_try_again": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930  \\u0928\\u0939\\u0940\\u0902 \\u092E\\u093F\\u0932\\u093E, \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902", "You_are_going_to_fix_the_appointment_for_patient_name": "\\u0906\\u092A \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0947 \\u0928\\u093E\\u092E \\u0915\\u0947 \\u0932\\u093F\\u090F \\u090F\\u0915 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u092C\\u0941\\u0915 \\u0915\\u0930\\u0928\\u0947 \\u091C\\u093E \\u0930\\u0939\\u0947 \\u0939\\u0948\\u0902", "Appointment_is_fixed": "\\u0905\\u092A\\u094D\\u0935\\u093E\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u092C\\u0941\\u0915 \\u0939\\u094B \\u091A\\u0941\\u0915\\u093E \\u0939\\u0948\\u0964", "This_patient_already_have_an_appointment_with_the_same_doctor_for_same_day": "\\u0907\\u0938 \\u0930\\u094B\\u0917\\u0940 \\u0915\\u0940 \\u092A\\u0939\\u0932\\u0947 \\u0938\\u0947 \\u0939\\u0940 \\u091A\\u092F\\u0928\\u093F\\u0924 \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u0947 \\u0938\\u093E\\u0925 \\u0909\\u0938\\u0940 \\u0926\\u093F\\u0928 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0939\\u0948\\u0964", "Do_you_want_to_continue": "\\u0915\\u094D\\u092F\\u093E \\u0906\\u092A \\u091C\\u093E\\u0930\\u0940 \\u0930\\u0916\\u0928\\u093E \\u091A\\u093E\\u0939\\u0924\\u0947 \\u0939\\u0948\\u0902?", "Problem_in_fixing_appointment": "\\u092C\\u0941\\u0915\\u093F\\u0902\\u0917 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "Appointment_Action": "\\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0915\\u0940 \\u0915\\u093E\\u0930\\u094D\\u0930\\u0935\\u093E\\u0908", "Start_Consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0947\\u0902", "Or_Cancel_Appointment": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930\\u0947\\u0902", "Local_Appointment_Action": "\\u0938\\u094D\\u0925\\u093E\\u0928\\u0940\\u092F \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0915\\u093E\\u0930\\u094D\\u0930\\u0935\\u093E\\u0908", "Start_Local_Consultation": "\\u0938\\u094D\\u0925\\u093E\\u0928\\u0940\\u092F \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0947\\u0902", "Problem_in_starting_consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "You_cannot_start_canceled_appointment": "\\u0906\\u092A \\u0930\\u0926\\u094D\\u0926 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0936\\u0941\\u0930\\u0942 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947\\u0964", "You_cannot_start_Completed_appointment": "\\u0906\\u092A \\u092A\\u0942\\u0930\\u094D\\u0923 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u092A\\u094D\\u0930\\u093E\\u0930\\u0902\\u092D \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947\\u0964", "Something_went_wrong_local_consultaion_Please_relogin": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E \\u0925\\u093E \\u0938\\u094D\\u0925\\u093E\\u0928\\u0940\\u092F \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0915\\u0943\\u092A\\u092F\\u093E \\u0932\\u0949\\u0917\\u093F\\u0928 \\u0915\\u0930\\u0947\\u0902", "You_cannot_cancel_active_appointment": "\\u0906\\u092A \\u0938\\u0915\\u094D\\u0930\\u093F\\u092F \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0915\\u094B \\u0930\\u0926\\u094D\\u0926 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947\\u0964", "No_Doctor_Available": "\\u0915\\u094B\\u0908 \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0928\\u0939\\u0940\\u0902", "Choose_Date": "\\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u091A\\u0941\\u0928\\u0947\\u0902", "Diognostic": "\\u0928\\u0948\\u0926\\u093E\\u0928\\u093F\\u0915", "Home": "\\u0939\\u094B\\u092E", "Suspend": "\\u0928\\u093F\\u0932\\u0902\\u092C\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Suspend_Consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0915\\u094B \\u0938\\u094D\\u0925\\u0917\\u093F\\u0924 \\u0915\\u0930\\u0947\\u0902", "Patient_Historty": "\\u0930\\u094B\\u0917\\u0940 \\u0915\\u093E \\u0907\\u0924\\u093F\\u0939\\u093E\\u0938", "complaints": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902", "Past_Report": "\\u092A\\u093F\\u091B\\u0932\\u0940 \\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F", "Parameters": "\\u092E\\u093E\\u092A\\u0926\\u0902\\u0921", "medication": "\\u0926\\u0935\\u093E\\u0908", "diagnosis": "\\u0928\\u093F\\u0926\\u093E\\u0928", "Investigations": "\\u091C\\u093E\\u0902\\u091A", "Instructions": "\\u0905\\u0928\\u0941\\u0936\\u0902\\u0938\\u093E\\u090F\\u0901", "Recent_complaints_(_click_on_complaint_name_to_add_)": "\\u0939\\u093E\\u0932 \\u0915\\u0940 \\u0936\\u093F\\u0915\\u093E\\u092F\\u0924\\u0947\\u0902\\u0964(Add \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F Complaint Name \\u092A\\u0930 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902)", "History_Of_Present_Illness": "\\u0935\\u0930\\u094D\\u0924\\u092E\\u093E\\u0928 \\u092C\\u0940\\u092E\\u093E\\u0930\\u0940 \\u0915\\u093E \\u0907\\u0924\\u093F\\u0939\\u093E\\u0938", "Past_Medical_or_Surgical_History": "\\u092A\\u093F\\u091B\\u0932\\u0947 \\u091A\\u093F\\u0915\\u093F\\u0924\\u094D\\u0938\\u093E \\u0938\\u0902\\u092C\\u0902\\u0927\\u0940 \\u092F\\u093E \\u0936\\u0932\\u094D\\u092F \\u091A\\u093F\\u0915\\u093F\\u0924\\u094D\\u0938\\u093E \\u0938\\u0902\\u092C\\u0902\\u0927\\u0940 \\u0907\\u0924\\u093F\\u0939\\u093E\\u0938", "Current_And_Recent_Medications": "\\u0935\\u0930\\u094D\\u0924\\u092E\\u093E\\u0928 \\u0914\\u0930 \\u0939\\u093E\\u0932 \\u0915\\u0940 \\u0926\\u0935\\u093E\\u090F\\u0902", "Other_Allergies_Or_Sensitivities": "\\u0905\\u0928\\u094D\\u092F \\u090F\\u0932\\u0930\\u094D\\u091C\\u0940 \\u092F\\u093E \\u0938\\u0902\\u0935\\u0947\\u0926\\u0928\\u0936\\u0940\\u0932\\u0924\\u093E", "Physical_Examination": "\\u0936\\u093E\\u0930\\u0940\\u0930\\u093F\\u0915 \\u092A\\u0930\\u0940\\u0915\\u094D\\u0937\\u093E", "Personal_History": "\\u0935\\u094D\\u092F\\u0915\\u094D\\u0924\\u093F\\u0917\\u0924 \\u0907\\u0924\\u093F\\u0939\\u093E\\u0938", "Family_History": "\\u092A\\u093E\\u0930\\u093F\\u0935\\u093E\\u0930\\u093F\\u0915 \\u0907\\u0924\\u093F\\u0939\\u093E\\u0938", "Medical_Allergies": "\\u092E\\u0947\\u0921\\u093F\\u0915\\u0932 \\u090F\\u0932\\u0930\\u094D\\u091C\\u0940", "Past_Consultation": "\\u0935\\u093F\\u0917\\u0924 \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936", "Past_Parameter": "\\u092A\\u093F\\u091B\\u0932\\u0947 \\u092E\\u093E\\u092A\\u0926\\u0902\\u0921", "upload_Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u0930\\u0947\\u0902", "Temperature": "\\u0924\\u093E\\u092A\\u092E\\u093E\\u0928", "Spo2": "\\u092A\\u0932\\u094D\\u0938 \\u0911\\u0915\\u094D\\u0938\\u0940\\u092E\\u0940\\u091F\\u0930", "PastRecord_ManualEntry_Msg": "\\u092E\\u0948\\u0928\\u0941\\u0905\\u0932 \\u090F\\u0902\\u091F\\u094D\\u0930\\u0940 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0917\\u094D\\u0930\\u093E\\u092B \\u092A\\u094D\\u0930\\u0926\\u0930\\u094D\\u0936\\u093F\\u0924 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u093F\\u092F\\u093E \\u091C\\u093E \\u0938\\u0915\\u0924\\u093E \\u0939\\u0948", "Glucose": "\\u0917\\u094D\\u0932\\u0942\\u0915\\u094B\\u091C", "Cholesterol": "\\u0915\\u094B\\u0932\\u0947\\u0938\\u094D\\u091F\\u094D\\u0930\\u0949\\u0932", "Hemoglobin": "\\u0939\\u0940\\u092E\\u094B\\u0917\\u094D\\u0932\\u094B\\u092C\\u093F\\u0928", "Systolic": "\\u0938\\u093F\\u0938\\u094D\\u091F\\u094B\\u0932\\u093F\\u0915", "Diastolic": "\\u0921\\u093E\\u092F\\u0938\\u094D\\u091F\\u094B\\u0932\\u093F\\u0915", "Pulse": "\\u0928\\u093E\\u0921\\u093C\\u0940", "Spo2_Percentage": "Spo2 \\u092A\\u094D\\u0930\\u0924\\u093F\\u0936\\u0924", "Spo2_Pulse": "\\u092A\\u0932\\u094D\\u0938 \\u0926\\u0930", "View_Consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0926\\u0947\\u0916\\u0947\\u0902", "Follow_up": "\\u091C\\u093E\\u0901\\u091A \\u0915\\u0930\\u0928\\u093E", "Finish_Consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0938\\u092E\\u093E\\u092A\\u094D\\u0924 \\u0915\\u0930\\u0947\\u0902", "Sucessfully_uploaded": "\\u0938\\u092B\\u0932\\u0924\\u093E\\u092A\\u0942\\u0930\\u094D\\u0935\\u0915 \\u0905\\u092A\\u0932\\u094B\\u0921 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E", "Print_Email_Consultation": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0938\\u0947 \\u092A\\u0939\\u0932\\u0947 \\u0906\\u0935\\u0936\\u094D\\u092F\\u0915 \\u0915\\u094D\\u0930\\u093F\\u092F\\u093E\\u0913\\u0902 \\u0915\\u094B \\u091A\\u0941\\u0928\\u0947\\u0902", "Connect_to_doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0938\\u0947 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0915\\u0930\\u0947\\u0902", "Back_To_Home": "\\u0935\\u093E\\u092A\\u0938", "Cancel_Appointment": "\\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930\\u0947\\u0902", "Problem_while_finish_consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0916\\u0924\\u094D\\u092E \\u0915\\u0930\\u0924\\u0947 \\u0938\\u092E\\u092F \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "This_consultation_cant_be_restart_do_you_want_to_continue": "\\u0906\\u092A \\u0907\\u0938 \\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0915\\u094B \\u0935\\u0949\\u0915-\\u0907\\u0928 \\u0935\\u093F\\u0915\\u0932\\u094D\\u092A \\u0938\\u0947 \\u092A\\u0941\\u0928\\u0903 \\u0906\\u0930\\u0902\\u092D \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0939\\u0948\\u0902\\u0964", "Options": "\\u0935\\u093F\\u0915\\u0932\\u094D\\u092A", "My_Calendar": "\\u092E\\u0947\\u0930\\u093E \\u0915\\u0948\\u0932\\u0947\\u0902\\u0921\\u0930", "You_have_to_find_doctor_first_to_fix_appointment": "\\u0906\\u092A\\u0915\\u094B \\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u092C\\u0941\\u0915 \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u092A\\u0939\\u0932\\u0947 \\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0915\\u094B \\u0922\\u0942\\u0902\\u0922\\u0928\\u093E \\u0939\\u094B\\u0917\\u093E", "configured_Slots": "\\u0915\\u0949\\u0928\\u094D\\u092B\\u093C\\u093F\\u0917\\u0930 \\u0915\\u093F\\u090F \\u0917\\u090F \\u0938\\u094D\\u0932\\u0949\\u091F", "Date1": "\\u0926\\u093F\\u0928\\u093E\\u0902\\u0915", "Duration": "\\u0905\\u0935\\u0927\\u093F", "Till_Time": "\\u0938\\u092E\\u092F \\u092A\\u0930", "Allow_General_Patients": "\\u0938\\u093E\\u092E\\u093E\\u0928\\u094D\\u092F \\u092E\\u0930\\u0940\\u091C\\u094B\\u0902 \\u0915\\u094B \\u0905\\u0928\\u0941\\u092E\\u0924\\u093F \\u0926\\u0947\\u0902", "Add_Slots": "\\u0938\\u094D\\u0932\\u0949\\u091F \\u091C\\u094B\\u0921\\u093C\\u0947\\u0902", "Please_Choose_Till_Date_Greater_than_From_Date": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915 \\u0926\\u093F\\u0928\\u093E\\u0902\\u0915 \\u0924\\u0915 \\u091A\\u0941\\u0928\\u0947\\u0902", "Please_Choose_From_Date": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0924\\u093E\\u0930\\u0940\\u0916 \\u0938\\u0947 \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "Please_Choose_Till_Date": "\\u0906\\u091C \\u0924\\u0915 \\u091A\\u0941\\u0928\\u0947\\u0902", "Please_Choose_From_Time": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u092E\\u092F \\u0938\\u0947 \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "Please_Choose_Till_Time": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u092E\\u092F \\u091A\\u0941\\u0928\\u0947\\u0902\\u0964", "Please_Choose_Till_Time_Greater_than_From_Time": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0938\\u092E\\u092F \\u0938\\u0947 \\u0905\\u0927\\u093F\\u0915 \\u0938\\u092E\\u092F \\u0924\\u0915 \\u091A\\u0941\\u0928\\u0947\\u0902", "Please_Choose_Duration": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u0905\\u0935\\u0927\\u093F \\u091A\\u0941\\u0928\\u0947\\u0902", "Are_you_sure_to_delete_these_slots": "\\u0915\\u094D\\u092F\\u093E \\u0906\\u092A \\u0935\\u093E\\u0915\\u0908 \\u0907\\u0928 \\u0938\\u094D\\u0932\\u0949\\u091F\\u094D\\u0938 \\u0915\\u094B \\u0939\\u091F\\u093E\\u0928\\u093E \\u091A\\u093E\\u0939\\u0924\\u0947 \\u0939\\u0948\\u0902?", "There_are_some_active_appointments_Are_you_sure_to_delete": "\\u0907\\u0928 \\u0938\\u094D\\u0932\\u0949\\u091F \\u092E\\u0947\\u0902 \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F\\u092F\\u093E\\u0902 \\u0924\\u092F \\u0915\\u0930 \\u0926\\u0940 \\u0917\\u0908 \\u0939\\u0948\\u0902\\u0964 \\u0915\\u094D\\u092F\\u093E \\u0906\\u092A \\u0935\\u093E\\u0915\\u0908 \\u0907\\u0928 \\u0938\\u094D\\u0932\\u0949\\u091F\\u094D\\u0938 \\u0915\\u094B \\u0939\\u091F\\u093E\\u0928\\u093E \\u091A\\u093E\\u0939\\u0924\\u0947 \\u0939\\u0948\\u0902?", "Slots_cannot_be_deleted": "\\u0938\\u094D\\u0932\\u0949\\u091F\\u094D\\u0938 \\u0915\\u094B \\u0939\\u091F\\u093E\\u092F\\u093E \\u0928\\u0939\\u0940\\u0902 \\u091C\\u093E \\u0938\\u0915\\u0924\\u093E", "You_cannot_cancel_these_slots_nurse_is_waiting": "\\u0906\\u092A \\u0907\\u0928 \\u0938\\u094D\\u0932\\u0949\\u091F \\u0915\\u094B \\u0930\\u0926\\u094D\\u0926 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0915\\u094D\\u092F\\u094B\\u0902\\u0915\\u093F \\u0928\\u0930\\u094D\\u0938 \\u0915\\u0928\\u0947\\u0915\\u094D\\u091F \\u0939\\u094B\\u0928\\u0947 \\u0915\\u0940 \\u092A\\u094D\\u0930\\u0924\\u0940\\u0915\\u094D\\u0937\\u093E \\u0915\\u0930 \\u0930\\u0939\\u0940 \\u0939\\u0948\\u0964", "You_can_start_only_active_appointments": "\\u0906\\u092A \\u0915\\u0947\\u0935\\u0932 \\u0938\\u0915\\u094D\\u0930\\u093F\\u092F \\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0939\\u0948\\u0902", "Problem_in_cancelling_consultation": "\\u092A\\u0930\\u093E\\u092E\\u0930\\u094D\\u0936 \\u0930\\u0926\\u094D\\u0926 \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E", "You_cannot_cancel_an_active_appointment": "\\u0906\\u092A \\u090F\\u0915 \\u0938\\u0915\\u094D\\u0930\\u093F\\u092F \\u0928\\u093F\\u092F\\u0941\\u0915\\u094D\\u0924\\u093F \\u0915\\u094B \\u0930\\u0926\\u094D\\u0926 \\u0928\\u0939\\u0940\\u0902 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947", "You_can_start_only_active_or_suspended_appointments": "\\u0906\\u092A \\u0915\\u0947\\u0935\\u0932 \\u0938\\u0915\\u094D\\u0930\\u093F\\u092F \\u092F\\u093E \\u0928\\u093F\\u0932\\u0902\\u092C\\u093F\\u0924 \\u0905\\u092A\\u0949\\u0907\\u0902\\u091F\\u092E\\u0947\\u0902\\u091F \\u0936\\u0941\\u0930\\u0942 \\u0915\\u0930 \\u0938\\u0915\\u0924\\u0947 \\u0939\\u0948\\u0902", "Remedi": "\\u0930\\u0947\\u092E\\u0947\\u0921\\u0940", "Logout": "\\u0932\\u094B\\u0917 \\u0906\\u0909\\u091F", "Change_Password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u092C\\u0926\\u0932\\u0947\\u0902", "Enter_Old_Password": "\\u092A\\u0941\\u0930\\u093E\\u0928\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0921\\u093E\\u0932\\u0947\\u0902", "Enter_New_Password": "\\u0928\\u092F\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Confirm_New_Password": "\\u0928\\u090F \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0915\\u0940 \\u092A\\u0941\\u0937\\u094D\\u091F\\u093F \\u0915\\u0930\\u0947\\u0902", "Please_Provide_Complete_Information": "\\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0942\\u0930\\u0940 \\u091C\\u093E\\u0928\\u0915\\u093E\\u0930\\u0940 \\u092A\\u094D\\u0930\\u0926\\u093E\\u0928 \\u0915\\u0930\\u0947\\u0902\\u0964", "New_Password_cannot_be_same_as_old": "\\u0928\\u092F\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u092A\\u0941\\u0930\\u093E\\u0928\\u0947 \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0915\\u0947 \\u0938\\u092E\\u093E\\u0928 \\u0928\\u0939\\u0940\\u0902 \\u0939\\u094B \\u0938\\u0915\\u0924\\u093E\\u0964", "Confirm_password_is_not_matched_with_new_password": "\\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u092E\\u0947\\u0932 \\u0928\\u0939\\u0940\\u0902 \\u0916\\u093E\\u0924\\u0947\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928", "Request_Coupons": "\\u0915\\u0942\\u092A\\u0928", "Coupon_count": "\\u0915\\u0942\\u092A\\u0928 \\u0915\\u0940 \\u0938\\u0902\\u0916\\u094D\\u092F\\u093E \\u0926\\u0930\\u094D\\u091C \\u0915\\u0930\\u0947\\u0902", "Request": "\\u0928\\u093F\\u0935\\u0947\\u0926\\u0928", "Click": "\\u0909\\u092A\\u0932\\u092C\\u094D\\u0927 \\u0915\\u0942\\u092A\\u0928 \\u092A\\u094D\\u0930\\u093E\\u092A\\u094D\\u0924 \\u0915\\u0930\\u0928\\u0947 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u092F\\u0939\\u093E\\u0902 \\u0915\\u094D\\u0932\\u093F\\u0915 \\u0915\\u0930\\u0947\\u0902", "Please_Contact_Admin": "\\u0915\\u0942\\u092A\\u0928 \\u0915\\u0947 \\u0932\\u093F\\u090F \\u0938\\u0902\\u092A\\u0930\\u094D\\u0915 \\u0935\\u094D\\u092F\\u0935\\u0938\\u094D\\u0925\\u093E\\u092A\\u0915", "Change_Language": "\\u092D\\u093E\\u0937\\u093E \\u092C\\u0926\\u0932\\u094B", "My_Profile": "\\u092E\\u0947\\u0930\\u0940 \\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932", "New_Password_should_be_six_character_long": "\\u0928\\u092F\\u093E \\u092A\\u093E\\u0938\\u0935\\u0930\\u094D\\u0921 \\u0915\\u092E \\u0938\\u0947 \\u0915\\u092E \\u091B\\u0939 \\u0935\\u0930\\u094D\\u0923 \\u0932\\u0902\\u092C\\u093E \\u0939\\u094B\\u0928\\u093E \\u091A\\u093E\\u0939\\u093F\\u090F\\u0964", "Hello_Admin": "\\u0939\\u0947\\u0932\\u094B \\u090F\\u0921\\u092E\\u093F\\u0928", "Profile_Management": "\\u092A\\u094D\\u0930\\u094B\\u092B\\u093C\\u093E\\u0907\\u0932 \\u092A\\u094D\\u0930\\u092C\\u0902\\u0927\\u0928", "Configuration": "\\u0935\\u093F\\u0928\\u094D\\u092F\\u093E\\u0938", "Report": "\\u0930\\u093F\\u092A\\u094B\\u0930\\u094D\\u091F", "Switch_Video": "\\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u092C\\u0926\\u0932\\u0947\\u0902", "Save_photo": "\\u0924\\u0938\\u094D\\u0935\\u0940\\u0930 \\u0915\\u094B  \\u0938\\u0902\\u0917\\u094D\\u0930\\u0939\\u0940\\u0924 \\u0915\\u0930\\u0947\\u0902", "Doctor": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930", "referrals": "\\u0930\\u0947\\u092B\\u093C\\u0930\\u0932", "Referral_Name": "\\u0935\\u093F\\u0936\\u0947\\u0937\\u091C\\u094D\\u091E\\u0924\\u093E", "complaint_name": "\\u0936\\u093F\\u0915\\u093E\\u092F\\u0924 \\u0915\\u093E \\u0928\\u093E\\u092E", "Drug_Class": "\\u0914\\u0937\\u0927\\u093F \\u0935\\u0930\\u094D\\u0917", "medication_Brand": "\\u0926\\u0935\\u093E \\u092C\\u094D\\u0930\\u093E\\u0902\\u0921", "Drug_Form": "\\u0921\\u094D\\u0930\\u0917 \\u092B\\u0949\\u0930\\u094D\\u092E", "Instruction": "\\u0905\\u0928\\u0941\\u0926\\u0947\\u0936", "Instruction_Category": "\\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936 \\u0936\\u094D\\u0930\\u0947\\u0923\\u0940", "Instruction_Name": "\\u0928\\u093F\\u0930\\u094D\\u0926\\u0947\\u0936 \\u0915\\u093E \\u0928\\u093E\\u092E", "ICD_Code": "\\u0906\\u0908\\u0938\\u0940\\u0921\\u0940 \\u0915\\u094B\\u0921", "Diagnosis_Name": "\\u0928\\u093F\\u0926\\u093E\\u0928 \\u0928\\u093E\\u092E", "Category_Name": "\\u0936\\u094D\\u0930\\u0947\\u0923\\u0940 \\u0928\\u093E\\u092E", "Category": "\\u0936\\u094D\\u0930\\u0947\\u0923\\u0940", "Investigation": "\\u091C\\u093E\\u0901\\u091A \\u092A\\u0921\\u093C\\u0924\\u093E\\u0932", "Test_Code": "\\u091F\\u0947\\u0938\\u094D\\u091F \\u0915\\u094B\\u0921", "lab_Name": "\\u0932\\u0948\\u092C \\u0928\\u093E\\u092E", "Location": "\\u0938\\u094D\\u0925\\u093E\\u0928", "Switch_to_low_bandwidth": "\\u0915\\u092E \\u092C\\u0948\\u0902\\u0921\\u0935\\u093F\\u0921\\u094D\\u0925 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902", "High_Bandwidth_video_mode_is_on": "\\u0909\\u091A\\u094D\\u091A \\u092C\\u0948\\u0902\\u0921\\u0935\\u093F\\u0921\\u094D\\u0925 \\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u0915\\u0949\\u0928\\u094D\\u092B\\u094D\\u0930\\u0947\\u0902\\u0938\\u093F\\u0902\\u0917 \\u0915\\u094B \\u091A\\u093E\\u0932\\u0942 \\u0915\\u0930 \\u0926\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Low_Bandwidth_video_mode_is_on": "\\u0915\\u092E \\u092C\\u0948\\u0902\\u0921\\u0935\\u093F\\u0921\\u094D\\u0925 \\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u0915\\u0949\\u0928\\u094D\\u092B\\u094D\\u0930\\u0947\\u0902\\u0938\\u093F\\u0902\\u0917 \\u0915\\u094B \\u091A\\u093E\\u0932\\u0942 \\u0915\\u093F\\u092F\\u093E \\u0917\\u092F\\u093E \\u0939\\u0948", "Switch_to_High_Bandwidth": "\\u0909\\u091A\\u094D\\u091A \\u092C\\u0948\\u0902\\u0921\\u0935\\u093F\\u0921\\u094D\\u0925 \\u092A\\u0930 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0947\\u0902", "Problem_in_switching_video_mode_Please_try_again": "\\u0935\\u0940\\u0921\\u093F\\u092F\\u094B \\u092E\\u094B\\u0921 \\u0938\\u094D\\u0935\\u093F\\u091A \\u0915\\u0930\\u0928\\u0947 \\u092E\\u0947\\u0902 \\u0938\\u092E\\u0938\\u094D\\u092F\\u093E \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928\\u0903 \\u092A\\u094D\\u0930\\u092F\\u093E\\u0938 \\u0915\\u0930\\u0947\\u0902", "Something_went_wrong_Please_try_again": "\\u0915\\u0941\\u091B \\u0917\\u0932\\u0924 \\u0939\\u094B \\u0917\\u092F\\u093E\\u0964 \\u0915\\u0943\\u092A\\u092F\\u093E \\u092A\\u0941\\u0928", "er_Name": "\\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0928\\u093E\\u092E", "User_Roles": "\\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u092D\\u0942\\u092E\\u093F\\u0915\\u093E", "Domains": "\\u0921\\u094B\\u092E\\u0947\\u0928", "Role_Mapping": "\\u092D\\u0942\\u092E\\u093F\\u0915\\u093E \\u092E\\u093E\\u0928\\u091A\\u093F\\u0924\\u094D\\u0930\\u0923", "Select_Domain": "\\u0921\\u094B\\u092E\\u0947\\u0928 \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Resource_Mapping": "\\u0938\\u0902\\u0938\\u093E\\u0927\\u0928 \\u092E\\u093E\\u0928\\u091A\\u093F\\u0924\\u094D\\u0930\\u0923", "Select_Role": "\\u092D\\u0942\\u092E\\u093F\\u0915\\u093E \\u0915\\u093E \\u091A\\u092F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Edit_Profile": "\\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932 \\u090F\\u0921\\u093F\\u091F \\u0915\\u0930\\u0947\\u0902", "Super_Admin": "\\u0938\\u0941\\u092A\\u0930 \\u090F\\u0921\\u092E\\u093F\\u0928", "Parameter_Mapping": "\\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930 \\u092E\\u093E\\u0928\\u091A\\u093F\\u0924\\u094D\\u0930\\u0923", "User_LoggedOut": "\\u0909\\u092A\\u092F\\u094B\\u0917\\u0915\\u0930\\u094D\\u0924\\u093E \\u0932\\u0949\\u0917\\u0911\\u0909\\u091F", "Doctor_Domain_Configuration": "\\u0921\\u0949\\u0915\\u094D\\u091F\\u0930 \\u0921\\u094B\\u092E\\u0947\\u0928 \\u0915\\u0949\\u0928\\u094D\\u092B\\u093C\\u093F\\u0917\\u0930\\u0947\\u0936\\u0928", "adminLogin": "\\u0935\\u094D\\u092F\\u0935\\u0938\\u094D\\u0925\\u093E\\u092A\\u0915 \\u0932\\u0949\\u0917\\u093F\\u0928 \\u0915\\u0930\\u0947\\u0902", "Profile": "\\u092A\\u094D\\u0930\\u094B\\u092B\\u093E\\u0907\\u0932", "Honorific": "\\u0938\\u0902\\u092E\\u093E\\u0928\\u093F\\u0924", "Name": "\\u0928\\u093E\\u092E", "Country": "\\u0926\\u0947\\u0936", "State": "\\u0930\\u093E\\u091C\\u094D\\u092F", "District": "\\u091C\\u093F\\u0932\\u093E", "Block": "\\u092C\\u094D\\u0932\\u0949\\u0915", "Village": "\\u0917\\u093E\\u0901\\u0935", "Remedi_Parameter": "\\u0930\\u0947\\u092E\\u0947\\u0921\\u0940 \\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930", "Other_Parameter": "\\u0905\\u0928\\u094D\\u092F \\u092A\\u0948\\u0930\\u093E\\u092E\\u0940\\u091F\\u0930", "ID": "\\u0906\\u0908\\u0921\\u0940", "status": "\\u0939\\u0948\\u0938\\u093F\\u092F\\u0924", "ok": "\\u0920\\u0940\\u0915 \\u0939\\u0948", "Domain": "\\u0921\\u094B\\u092E\\u0947\\u0928", "Resources": "\\u0938\\u0902\\u0938\\u093E\\u0927\\u0928", "Resource": "\\u0938\\u0902\\u0938\\u093E\\u0927\\u0928", "Role": "\\u092D\\u0942\\u092E\\u093F\\u0915\\u093E"}