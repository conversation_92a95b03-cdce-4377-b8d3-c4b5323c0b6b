import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {FormGroup,} from "@angular/forms";
import { Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import * as Highcharts from 'highcharts/highstock';
import { Subscription } from 'rxjs';
import { webSocket,WebSocketSubject } from 'rxjs/webSocket';
import { WebsocketsService } from '../../../../core/services/websocket.service';
import { ModalController } from '@ionic/angular';
import { HttpClient } from '@angular/common/http';
import { ConstantService } from 'src/app/core/services/constant.service';
import { ApiService } from 'src/app/core/services/api.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import * as CryptoJS from 'crypto-js';

export interface ECGResponse {
  status: string;
  message: any;
}

@Component({
  selector: 'app-ecg-jetty-device',
  templateUrl: './ecg-jetty-device.page.html',
  styleUrls: ['./ecg-jetty-device.page.scss'],
  standalone: true,
  imports: [MatTooltipModule,MatDialogModule, CommonModule, FormsModule]
})
export class EcgJettyDevicePage implements OnInit {

  username: any;
  nPointsPerFrame: number = 10; // This value determines the speed. Higher value means more speed.
  YRatio: number = 0.75;
  ecgPulse: number = 0;
  Lead_1: number[] = [];
  Lead_2: number[] = [];
  Lead_3: number[] = [];
  AVR: number[] = [];
  AVL: number[] = [];
  AVF: number[] = [];
  V_1: number[] = [];
  V_2: number[] = [];
  V_3: number[] = [];
  V_4: number[] = [];
  V_5: number[] = [];
  V_6: number[] = [];
  allLeadsData: number[][] = []; // Array to store all lead data
  secondLeadsData: number[][] = [];
  prevMMSel: any;
  whichMM: string = "";
  curFrame_Lead_1: number = 0; // Starts with frame 0.
  curFrame_Lead_2: number = 0;
  curFrame_Lead_3: number = 0;
  curFrame_AVF: number = 0;
  curFrame_AVL: number = 0;
  curFrame_AVR: number = 0;
  curFrame_V_1: number = 0;
  curFrame_V_2: number = 0;
  curFrame_V_3: number = 0;
  curFrame_V_4: number = 0;
  curFrame_V_5: number = 0;
  // FIRST GRAPH Lead_1
  // position that will be treated as 0,0 when drawing our points.
  originX_Lead_1: number = 27;
  originY_Lead_1: number = 5;
  // SECOND GRAPH Lead_2
  originX_Lead_2: number = 27;
  originY_Lead_2: number = 100;
  // THIRD GRAPH Lead_3
  originX_Lead_3: number = 27;
  originY_Lead_3: number = 194;
  // FOURTH GRAPH AVR
  originX_AVR: number = 27;
  originY_AVR: number = 289;
  curFrame_V_6: number = 0;

  // FIFTH GRAPH AVL
  originX_AVL: number = 27;
  originY_AVL: number = 383;
  // SIXTH GRAPH AVF
  originX_AVF: number = 27;
  originY_AVF: number = 478;
  // SEVENTH GRAPH V_1
  originX_V_1: number = 537;
  originY_V_1: number = 5;
  // EIGTH GRAPH V_2
  originX_V_2: number = 537;
  originY_V_2: number = 100;
  // NINTH GRAPH V_3
  originX_V_3: number = 537;
  originY_V_3: number = 194;
  // TENTH GRAPH V_4
  originX_V_4: number = 537;
  originY_V_4: number = 289;
  // ELEVENTH GRAPH V_5
  originX_V_5: number = 537;
  originY_V_5: number = 383;
  // TWELFTH GRAPH V_6
  originX_V_6: number = 537;
  originY_V_6: number = 478;

  origin20X_V_2: number = 250;
  origin20X_V_3: number = 452;
  origin20X_V_4: number = 652;
  origin20X_V_5: number = 850;
  origin20X_V_6: number = 1000;
  curFrame20_Lead_2: number = 250;
  curFrame20_Lead_3: number = 452;
  originX20_AVR: number = 653;
  originX20_AVL = 850;
  originY20_AVF: number = 1000

  ecgContents: boolean = true;


  whichMV!: string;
  sampling_rate!: number;
  ecgDevicehighchart: any = Highcharts;
  ecgDevicehighchartNew: any = Highcharts;
  ecgDeviceData: any;
  first6LeadsData: number[][] = []; // Array for the first 6 leads (Lead_1 to AVF)
  second6LeadsData: number[][] = []; // Array for the second 6 leads (V_1 to V_6)
  // first6LeadsData = []; // Ensure this is initialized as a 3D array
  // second6LeadsData = []; // Ensure this is initialized as a 3D array

  ecgDeviceChart: any;
  ecgDeviceSeries: any;
  ecgDeviceSetScrollFlag: any;
  ecgDeviceGraph: any;
  isBLE: any;
  heartPulse: any;
  showButton = false;
  showButtonPrint = false;
  showPrintlive = false;
  showPrintPast = false;
  noData = false;
  tokenRequest: any;
  combinedLeadsData: number[][] = []; // To store combined first 6 leads
  combinedSecondLeadsData: number[][] = []; // To store combined second 6 leads

  // Flags to ensure data is only processed once per set
  isFirstSetCollected: boolean = false;
  isSecondSetCollected: boolean = false;
  showEcgHeader: boolean = false;
  showEcgContent: boolean = false;
  /**?
   * Variables
   */
  battery: number = 0;
  color = "#3DCC93";
  percentage = 80;
  addForm!: FormGroup;
  currDomainName: any;
  currDocId: any;
  currDomainId: any;
  currUserType: any;
  getPatientData: any;
  websock!: WebSocketSubject<any>;
  setValueSub!: Subscription;
  filePath = "";
  secretKey: string =
    "19f148fe75a9b266fd6398ccc3616a12d26bd67acfcbaa81654bfdb08c8dc51c"; //bit arrary
  usingBleSensor: any;
  V1: any;
  V2: any;
  V3: any;
  V4: any;
  V5: any;
  V6: any;
  isEcgReadingStarted: any;
  constructor(
    private websocketsService: WebsocketsService,
    private modalCtrl: ModalController,
    private http: HttpClient,
    private constantSvc: ConstantService,
    private apiSvc: ApiService,
    public dialogRef: MatDialogRef<EcgJettyDevicePage>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }
  closeModal() {
    this.modalCtrl.dismiss();
  }
  valSubscription!: Subscription;
  ecgDeviceVal: any = [];
  ecgDeviceErrorMessage: any;
  spoTowPercentage: any;
  pulseData = 0;
  ngOnInit(): void {
    this.isBLE = "true";
    this.ecgDeviceData = [];
    this.ecgDeviceSetScrollFlag = 0;
    const currentUser = JSON.parse(sessionStorage.getItem("USER") || '{}');
    console.log('Battery value:', this.battery);

    this.tokenRequest = sessionStorage.getItem("token");
    // this.secretKeyRequest = sessionStorage.getItem('secretKey');
    // console.log("secretKey", this.secretKeyRequest);
    this.currDomainName = currentUser?.commondetail?.domainName ?? '';
    this.username = currentUser?.profiledetail?.userName ?? '';
    this.currUserType = currentUser?.commondetail?.usertype ?? '';
    //this.currDomainId = JSON.parse(sessionStorage.getItem("domainId")|| '{}');
    // this.getPatientData = this.helperSvc.getOption();
    // this.getPatientData = JSON.parse(sessionStorage.getItem("patientDetails")|| '{}');
    console.log("this.data", this.data);
    if (this.data.api == "withoutApiCalling") {
      (this.byId("ecgContent") as HTMLElement).style.display = "block";
      (this.byId("ecgContent") as HTMLElement).style.display = "block";


      // this.byId('ecgPulseRate').innerHTML;
      this.createGraphPaper();
      var d1Send = true;
      var d2Send = true;


      this.valSubscription = this.websocketsService.bleValueObs$.subscribe((data: string) => {
        console.log("Data from WebSocket service ecgDeviceData", data);

        // Handle ECG lead data
        if (data.match(/setEcgLead/g)) {
          this.showButton = false;
          this.showPrintPast = false;
          this.showPrintlive = true;
          this.showButtonPrint = true;
          // Parse and store the lead data
          try {
            this.ecgDeviceVal = JSON.parse(data.split("~")[1]);
            console.log("ECG", this.ecgDeviceVal);

            // Check if each lead has exactly 1250 data points
            if (this.ecgDeviceVal.length !== 1250) {
              console.warn(`Received lead with incorrect length: ${this.ecgDeviceVal.length}`);
              return; // Exit if data is incomplete
            }

            // Identify which lead was sent and store accordingly
            if (data.match(/Lead_1/g)) this.Lead_1 = this.ecgDeviceVal;
            else if (data.match(/Lead_2/g)) this.Lead_2 = this.ecgDeviceVal;
            else if (data.match(/Lead_3/g)) this.Lead_3 = this.ecgDeviceVal;
            else if (data.match(/AVR/g)) this.AVR = this.ecgDeviceVal;
            else if (data.match(/AVL/g)) this.AVL = this.ecgDeviceVal;
            else if (data.match(/AVF/g)) this.AVF = this.ecgDeviceVal;
            else if (data.match(/V_1/g)) this.V_1 = this.ecgDeviceVal;
            else if (data.match(/V_2/g)) this.V_2 = this.ecgDeviceVal;
            else if (data.match(/V_3/g)) this.V_3 = this.ecgDeviceVal;
            else if (data.match(/V_4/g)) this.V_4 = this.ecgDeviceVal;
            else if (data.match(/V_5/g)) this.V_5 = this.ecgDeviceVal;
            else if (data.match(/V_6/g)) this.V_6 = this.ecgDeviceVal;

            // Handle the first set of leads: Lead_1, Lead_2, Lead_3, AVR, AVL, AVF
            if (
              this.Lead_1?.length === 1250 &&
              this.Lead_2?.length === 1250 &&
              this.Lead_3?.length === 1250 &&
              this.AVR?.length === 1250 &&
              this.AVL?.length === 1250 &&
              this.AVF?.length === 1250
            ) {
              this.first6LeadsData = [];  // Ensure it's reset before pushing new data
              this.first6LeadsData.push(this.Lead_1, this.Lead_2, this.Lead_3, this.AVR, this.AVL, this.AVF);
              console.log("First 6 Leads Data (2D array):", this.first6LeadsData, "Length:", this.first6LeadsData.length);
            }

            // Handle the second set of leads: V_1, V_2, V_3, V_4, V_5, V_6
            if (
              this.V_1?.length === 1250 &&
              this.V_2?.length === 1250 &&
              this.V_3?.length === 1250 &&
              this.V_4?.length === 1250 &&
              this.V_5?.length === 1250 &&
              this.V_6?.length === 1250
            ) {
              this.second6LeadsData = [];  // Ensure it's reset before pushing new data
              this.second6LeadsData.push(this.V_1, this.V_2, this.V_3, this.V_4, this.V_5, this.V_6);
              console.log("Second 6 Leads Data (2D array):", this.second6LeadsData, "Length:", this.second6LeadsData.length);
            }
          } catch (error) {
            console.error("Error parsing ECG data:", error);
          }
        }

        // Log the lengths of the lead arrays for debugging
        console.log(
          "Leads V_1 to V_6 lengths:",
          this.V_1?.length,
          this.V_2?.length,
          this.V_3?.length,
          this.V_4?.length,
          this.V_5?.length,
          this.V_6?.length
        );
        console.log(
          "Leads Lead_1 to AVR lengths:",
          this.Lead_1?.length,
          this.Lead_2?.length,
          this.Lead_3?.length,
          this.AVR?.length,
          this.AVL?.length,
          this.AVF?.length
        );

        // Handle other WebSocket messages (e.g., battery status, error messages)
        if (data.match(/errorLableUpdate/g)) {
          this.ecgDeviceErrorMessage = data.split("~")[1];
        } else if (data.match(/plotEcgData/g)) {
          this.plotGraphs();
          this.isEcgReadingStarted = true;
        } else if (data.match(/pulseOfECG/g)) {
          this.pulseData = Number(data.split("~")[1]);

          // Trigger getECGPast() if first set of leads is complete
          if (
            d1Send &&
            this.Lead_1.length > 0 &&
            this.Lead_2.length > 0 &&
            this.Lead_3.length > 0 &&
            this.AVR.length > 0 &&
            this.AVL.length > 0 &&
            this.AVF.length > 0
          ) {
            d1Send = false;
            console.log("INSIDE THE BLOCK where save ecg is getting called 88888888888888");
             this.getECGPast();
          }
          // Trigger getECGSecondPast() and save data if second set of leads is complete
          if (
            d2Send &&
            this.V_1.length > 0 &&
            this.V_2.length > 0 &&
            this.V_3.length > 0 &&
            this.V_4.length > 0 &&
            this.V_5.length > 0 &&
            this.V_6.length > 0
          ) {
            d2Send = false;
            this.getECGSecondPast();
          }
        }
        // Update battery status
        if (data.match(/changeBatteryStatus/g)) {
          const batteryBit = Number(data.split("~")[1]);
          this.battery = [10, 33, 66, 100][batteryBit - 4] || 0;
        }
      });

    }
    //}
  }

  ngOnDestroy() {
    clearInterval(this.printInterval);
  }
  onDialogClose() {
     if (this.isEcgReadingStarted) {
    this.websocketsService.sendBleMessage("disableNotificationFromHtml");
    this.isEcgReadingStarted = false;
  }
   this.dialogRef.close('closedByUser'); 
  }
  terViewInit() { }
  selectMMchange(event: any) {
    /*var mm=$("#mmSelect option:selected").val();
    whichMM=mm;*/
    console.log("selectMMchange", event, event.target.value);

    let seletedmm = event.target.value;
    this.whichMM = seletedmm;
    // whichMM="10";
    const pulseYAxis = document.getElementById("pulse_yaxis");
    if (pulseYAxis) {
      pulseYAxis.innerHTML = seletedmm;
    }

    this.resetAxis();
    // this.createGraphPaper();
    this.plotGrid();
    this.onDocLoadedChanged();
  }

  createGraphPaper() {
    /** Data from BLE**/
    this.originY_Lead_1 = 5 + 50;
    this.originY_Lead_2 = 100 + 50;
    this.originY_Lead_3 = 194 + 50;
    this.originY_AVR = 289 + 50;
    this.originY_AVL = 383 + 50;
    this.originY_AVF = 478 + 50;
    this.originY_V_1 = 5 + 50;
    this.originY_V_2 = 100 + 50;
    this.originY_V_3 = 194 + 50;
    this.originY_V_4 = 289 + 50;
    this.originY_V_5 = 383 + 50;
    this.originY_V_6 = 478 + 50;
    this.plotGrid();

  }
  resetAxis() {
    this.curFrame_Lead_1 = 0;
    this.curFrame_Lead_2 = 0;
    this.curFrame_Lead_3 = 0;
    this.curFrame_AVF = 0;
    this.curFrame_AVL = 0;
    this.curFrame_AVR = 0;
    this.curFrame_V_1 = 0;
    this.curFrame_V_2 = 0;
    this.curFrame_V_3 = 0;
    this.curFrame_V_4 = 0;
    this.curFrame_V_5 = 0;
    this.curFrame_V_6 = 0;
  }
  containerHeight = "500px";
  canvasHeight = 565;


  onDocLoadedChanged(): void {
    let Operation_val: number;

    // Handle different measurement modes
    if (this.whichMM === "20") {
      console.log("inside 20mm and prevMMSel===" + this.prevMMSel);
      document.getElementById("ecgcanvas-container")!.style.height = "600px";
      let ecgCanvas = document.getElementById("ecg_canvas") as HTMLCanvasElement;
      this.drawBkg(ecgCanvas, 3.78, "0.25", "green", 20);
      console.log(this.prevMMSel + "-------------------------------");

      Operation_val = this.prevMMSel === 10 ? 2 : this.prevMMSel === 5 ? 4 : 1;
      this.prevMMSel = 20;

      // Multiply leads by Operation_val
      this.adjustLeadValues(Operation_val);

      // Define a starting vertical offset and an increment value to space each graph
      let startingYOffset = 50; // Initial vertical position
      let offsetIncrement = 100; // Increment for each graph's vertical spacing
      this.drawLeadsWithOffset(startingYOffset, offsetIncrement, true);

    } else if (this.whichMM === "5") {
      console.log("inside 5mm and prevMMSel===" + this.prevMMSel);
      document.getElementById("ecgcanvas-container")!.style.height = "500px";
      let ecgCanvas = document.getElementById("ecg_canvas") as HTMLCanvasElement;
      ecgCanvas.height = 565;
      this.drawBkg(ecgCanvas, 3.78, "0.25", "green", 5);

      Operation_val = this.prevMMSel === 10 ? 2 : this.prevMMSel === 20 ? 4 : 1;
      this.prevMMSel = 5;

      this.adjustLeadValues(1 / Operation_val);
      this.drawLeadsWithOffset(50, 100, false); // Use the standard drawing method

    } else if (this.whichMM === "10") {
      console.log("inside default 10mm and prevMMSel===" + this.prevMMSel);
      document.getElementById("ecgcanvas-container")!.style.height = "500px";
      let ecgCanvas = document.getElementById("ecg_canvas") as HTMLCanvasElement;
      ecgCanvas.height = 565;
      this.drawBkg(ecgCanvas, 3.78, "0.25", "green", 10);

      if (this.prevMMSel === 20) {
        Operation_val = 2;
        this.adjustLeadValues(1 / Operation_val);
      } else if (this.prevMMSel === 5) {
        Operation_val = 2;
        this.adjustLeadValues(Operation_val);
      } else {
        Operation_val = 1;
        this.adjustLeadValues(Operation_val);
      }
      this.prevMMSel = 10;
      this.drawLeadsWithOffset(50, 100, false); // Use the standard drawing method
    }

    // Set sampling rate based on whichMV
    this.setSamplingRate();

    // Log the current date and lead data
    this.logLeadData();
  }

  adjustLeadValues(Operation_val: number): void {
    [this.Lead_1, this.Lead_2, this.Lead_3, this.AVR, this.AVL, this.AVF, this.V_1, this.V_2, this.V_3, this.V_4, this.V_5, this.V_6].forEach(leadArray => {
      for (let i = 0; i < leadArray.length; i++) {
        leadArray[i] *= Operation_val;
      }
    });
  }

  drawLeadsWithOffset(startingYOffset: number, offsetIncrement: number, is20mm: boolean): void {
    if (is20mm) {
      this.drawcurFrame20_Lead_1();
      this.drawcurFrame20_Lead_2();
      this.drawcurFrame20_Lead_3();
      this.drawcurFrame20_AVR();
      this.drawcurFrame20_AVL();
      this.drawcurFrame20_AVF();
      this.drawcurFrame20_V_1();
      this.drawcurFrame20_V_2();
      this.drawcurFrame20_V_3();
      this.drawcurFrame20_V_4(this.V_4);
      this.drawcurFrame20_V_5(this.V_5);
      this.drawcurFrame20_V_6();
    } else {
      this.drawcurFrame_Lead_1();
      this.drawcurFrame_Lead_2();
      this.drawcurFrame_Lead_3();
      this.drawcurFrame_AVR();
      this.drawcurFrame_AVL();
      this.drawcurFrame_AVF();
      this.drawcurFrame_V_1();
      this.drawcurFrame_V_2();
      this.drawcurFrame_V_3();
      this.drawcurFrame20_V_4(this.V_4);
      this.drawcurFrame20_V_5(this.V_5);
      this.drawcurFrame_V_6();
    }
  }


  setSamplingRate(): void {
    if (this.whichMV === "25") {
      this.sampling_rate = 0.377;
    } else if (this.whichMV === "50") {
      this.sampling_rate = 0.754;
    } else if (this.whichMV === "12") {
      this.sampling_rate = 0.1885;
    }
  }

  logLeadData(): void {
    let currentdate1 = new Date();
    console.log("Here3 ...." + currentdate1);
    console.log("Ranjan::lead1=" + this.Lead_1);

    // Final log of the current date and lead lengths
    let currentdate = new Date();
    console.log("Here3 ...." + currentdate);
    console.log("lead_1.length==" + this.Lead_1.length);
  }





  multiplyLead(Operation_val: any) {
    for (let i = 0; i < this.Lead_1.length; i++) {
      this.Lead_1[i] *= Operation_val;
      this.Lead_2[i] *= Operation_val;
      this.Lead_3[i] *= Operation_val;
      this.AVR[i] *= Operation_val;
      this.AVL[i] *= Operation_val;
      this.AVF[i] *= Operation_val;
      this.V_1[i] *= Operation_val;
      this.V_2[i] *= Operation_val;
      this.V_3[i] *= Operation_val;
      this.V_4[i] *= Operation_val;
      this.V_5[i] *= Operation_val;
      this.V_6[i] *= Operation_val;
    }
  }
  divideLead(Operation_val: any) {
    for (var i = 0; i < this.Lead_1.length; i++) {
      this.Lead_1[i] /= Operation_val;
      this.Lead_2[i] /= Operation_val;
      this.Lead_3[i] /= Operation_val;
      this.AVR[i] /= Operation_val;
      this.AVL[i] /= Operation_val;
      this.AVF[i] /= Operation_val;
      this.V_1[i] /= Operation_val;
      this.V_2[i] /= Operation_val;
      this.V_3[i] /= Operation_val;
      this.V_4[i] /= Operation_val;
      this.V_5[i] /= Operation_val;
      this.V_6[i] /= Operation_val;
    }
  }
  byId(id: string): HTMLCanvasElement | null {
    return document.getElementById(id) as HTMLCanvasElement | null;
  }
  plotGrid() {
    this.drawBkg(this.byId("ecg_canvas"), 3.78, "0.25", "green", 0);
  }



  drawBkg(
    canvasElem: HTMLCanvasElement | null,
    squareSize: number,
    minorLineWidthStr: string,
    lineColStr: string,
    whichMM: number
  ) {
    if (!canvasElem) return;

    const ctx = canvasElem.getContext("2d");
    if (!ctx) return;

    ctx.clearRect(0, 0, canvasElem.width, canvasElem.height);

    let curX = 0;
    let nLinesDone = 0;
    ctx.strokeStyle = lineColStr;

    // Vertical lines
    while (curX < canvasElem.width) {
      ctx.lineWidth = nLinesDone % 5 === 0 ? 0.6 : parseFloat(minorLineWidthStr);
      ctx.beginPath();
      ctx.moveTo(curX, 0);
      ctx.lineTo(curX, canvasElem.height);
      ctx.stroke();
      curX += squareSize;
      nLinesDone++;
    }

    // Horizontal lines
    let curY = 0;
    nLinesDone = 0;
    while (curY < canvasElem.height) {
      ctx.lineWidth = nLinesDone % 5 === 0 ? 0.6 : parseFloat(minorLineWidthStr);
      ctx.beginPath();
      ctx.moveTo(0, curY);
      ctx.lineTo(canvasElem.width, curY);
      ctx.stroke();
      curY += squareSize;
      nLinesDone++;
    }
    // Drawing custom white rectangles based on `whichMM`
    const drawWhiteRect = (x: number, y: number, width: number, height: number) => {
      ctx.rect(x, y, width, height);
      ctx.fillStyle = "white";
      ctx.fill();
      ctx.lineWidth = 0;
      ctx.strokeStyle = "white";
      ctx.stroke();
    };

    // Draw Rectangles - Lead Boxes
    if (whichMM === 20) {
      drawWhiteRect(28, 105, 44, 20);
      drawWhiteRect(28, 290, 48, 20);
      drawWhiteRect(28, 494, 52, 20);
      drawWhiteRect(28, 688, 32, 20);
      drawWhiteRect(28, 877, 32, 20);
      drawWhiteRect(28, 1042, 32, 20);
      drawWhiteRect(537, 105, 25, 20);
      drawWhiteRect(537, 290, 25, 20);
      drawWhiteRect(537, 494, 25, 20);
      drawWhiteRect(537, 688, 25, 20);
      drawWhiteRect(537, 877, 25, 20);
      drawWhiteRect(537, 1042, 25, 20);
    } else {
      drawWhiteRect(28, 65, 44, 20);
      drawWhiteRect(28, 160, 48, 20);
      drawWhiteRect(28, 254, 52, 20);
      drawWhiteRect(28, 348, 32, 20);
      drawWhiteRect(28, 443, 32, 20);
      drawWhiteRect(28, 537, 32, 20);
      drawWhiteRect(537, 65, 25, 20);
      drawWhiteRect(537, 160, 25, 20);
      drawWhiteRect(537, 254, 25, 20);
      drawWhiteRect(537, 348, 25, 20);
      drawWhiteRect(537, 443, 25, 20);
      drawWhiteRect(537, 537, 25, 20);
    }

    // Adding Labels - Lead Text
    const addLeadText = (text: string, x: number, y: number) => {
      ctx.fillStyle = "black";
      ctx.font = "16px Times New Roman";
      ctx.fillText(text, x, y);
    };

    if (whichMM === 20) {
      addLeadText("Lead_1", 29, 123);
      addLeadText("Lead_2", 29, 309);
      addLeadText("Lead_3", 29, 513);
      addLeadText("AVR", 29, 707);
      addLeadText("AVL", 29, 896);
      addLeadText("AVF", 29, 1061);
      addLeadText("V1", 538, 123);
      addLeadText("V2", 538, 309);
      addLeadText("V3", 538, 513);
      addLeadText("V4", 538, 707);
      addLeadText("V5", 538, 891);
      addLeadText("V6", 538, 1061);
    } else {
      addLeadText("Lead_1", 29, 83);
      addLeadText("Lead_2", 29, 179);
      addLeadText("Lead_3", 29, 273);
      addLeadText("AVR", 29, 367);
      addLeadText("AVL", 29, 461);
      addLeadText("AVF", 29, 556);
      addLeadText("V1", 538, 83);
      addLeadText("V2", 538, 179);
      addLeadText("V3", 538, 273);
      addLeadText("V4", 538, 367);
      addLeadText("V5", 538, 461);
      addLeadText("V6", 538, 556);
    }
  }


  Draw_Lead(
    nSamplesToDraw: number,
    firstSample: number,
    lineWidthStr: string | number,
    lineColourStr: string | CanvasGradient | CanvasPattern,
    originX_Lead: number,
    originY_Lead: number,
    leadNumber: number[] = [],
    whichMM: string | number
  ) {
    var can = <HTMLCanvasElement>this.byId("ecg_canvas");
    var ctx = can.getContext("2d");
    if (!ctx) return;
    ctx.strokeStyle = lineColourStr;
    ctx.lineWidth = typeof lineWidthStr === "string" ? parseFloat(lineWidthStr) : lineWidthStr;


    ctx.beginPath();
    var offsetY = 38;
    if (whichMM == 20) {
      ctx.moveTo(
        originX_Lead + firstSample * 0.377,
        Number(originY_Lead) + leadNumber[firstSample - 1] * this.YRatio * -1

      );
    } else {
      ctx.moveTo(
        originX_Lead + firstSample * 0.377,
        originY_Lead + leadNumber[firstSample - 1] * this.YRatio * -1
      );
    }

    for (let i = 0; i < nSamplesToDraw; i++) {
      var curSample = leadNumber[i + firstSample]; // calculate y coordinate
      if (whichMM == 20) {
        ctx.lineTo(
          originX_Lead + (firstSample + i) * 0.377,
          originY_Lead + (curSample * this.YRatio * -1 + offsetY)
        );
      } else {
        ctx.lineTo(
          originX_Lead + (firstSample + i) * 0.377,
          originY_Lead + curSample * this.YRatio * -1
        );
      }
    }

    ctx.stroke();
  }

  drawcurFrame_Lead_1() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_1,
      "1",
      "red",
      this.originX_Lead_1,
      this.originY_Lead_1,
      this.Lead_1,
      this.whichMM
    ); // Calling function to draw the graph
    this.curFrame_Lead_1 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_1 < 130) {
      // requestAnimationFrame(this.drawcurFrame_Lead_1);
      requestAnimationFrame(this.drawcurFrame_Lead_1.bind(this));
    }
  }
  drawcurFrame_Lead_2() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_2,
      "1",
      "red",
      this.originX_Lead_2,
      this.originY_Lead_2,
      this.Lead_2,
      this.whichMM
    );
    this.curFrame_Lead_2 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_2 < 130)
      requestAnimationFrame(this.drawcurFrame_Lead_2.bind(this));
  }

  drawcurFrame_Lead_3() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_3,
      "1",
      "red",
      this.originX_Lead_3,
      this.originY_Lead_3,
      this.Lead_3,
      this.whichMM
    );
    this.curFrame_Lead_3 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_3 < 130)
      requestAnimationFrame(this.drawcurFrame_Lead_3.bind(this));
  }
  drawcurFrame_AVR() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVR,
      "1",
      "red",
      this.originX_AVR,
      this.originY_AVR,
      this.AVR,
      this.whichMM
    );
    this.curFrame_AVR += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVR < 130)
      requestAnimationFrame(this.drawcurFrame_AVR.bind(this));
  }

  drawcurFrame_AVL() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVL,
      "1",
      "red",
      this.originX_AVL,
      this.originY_AVL,
      this.AVL,
      this.whichMM
    );
    this.curFrame_AVL += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVL < 130)
      requestAnimationFrame(this.drawcurFrame_AVL.bind(this));
  }
  drawcurFrame_AVF() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVF,
      "1",
      "red",
      this.originX_AVF,
      this.originY_AVF,
      this.AVF,
      this.whichMM
    );

    this.curFrame_AVF += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVF < 130)
      requestAnimationFrame(this.drawcurFrame_AVF.bind(this));
  }

  drawcurFrame_V_1() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_1,
      "1",
      "red",
      this.originX_V_1,
      this.originY_V_1,
      this.V_1,
      this.whichMM
    );

    this.curFrame_V_1 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_1 < 130)
      requestAnimationFrame(this.drawcurFrame_V_1.bind(this));
  }

  drawcurFrame_V_2() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_2,
      "1",
      "red",
      this.originX_V_2,
      this.originY_V_2,
      this.V_2,
      this.whichMM
    );
    this.curFrame_V_2 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_2 < 130)
      requestAnimationFrame(this.drawcurFrame_V_2.bind(this));
  }

  drawcurFrame_V_3() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_3,
      "1",
      "red",
      this.originX_V_3,
      this.originY_V_3,
      this.V_3,
      this.whichMM
    );
    this.curFrame_V_3 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_3 < 130)
      requestAnimationFrame(this.drawcurFrame_V_3.bind(this));
  }

  drawcurFrame_V_4(V_4: any) {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_4,
      "1",
      "red",
      this.originX_V_4,
      this.originY_V_4,
      this.V_4,
      this.whichMM
    );
    this.curFrame_V_4 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_4 < 130)
      requestAnimationFrame(this.drawcurFrame_V_4.bind(this));
  }

  drawcurFrame_V_5(V_5: any) {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_5,
      "1",
      "red",
      this.originX_V_5,
      this.originY_V_5,
      this.V_5,
      this.whichMM
    );
    this.curFrame_V_5 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_5 < 130)
      requestAnimationFrame(this.drawcurFrame_V_5.bind(this));
  }

  drawcurFrame_V_6() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_6,
      "1",
      "red",
      this.originX_V_6,
      this.originY_V_6,
      this.V_6,
      this.whichMM
    );
    this.curFrame_V_6 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_6 < 130)
      requestAnimationFrame(this.drawcurFrame_V_6.bind(this));
  }


  drawcurFrame20_Lead_1() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_1,
      "1",
      "red",
      this.originX_Lead_1,
      this.originY_Lead_1,
      this.Lead_1,
      this.whichMM
    ); // Calling function to draw the graph
    this.curFrame_Lead_1 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_1 < 130) {
      // requestAnimationFrame(this.drawcurFrame_Lead_1);
      requestAnimationFrame(this.drawcurFrame20_Lead_1.bind(this));
    }
  }
  drawcurFrame20_Lead_2() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_2,
      "1",
      "red",
      this.originX_Lead_2,
      this.originY_Lead_2,
      this.Lead_2,
      this.whichMM
    );
    this.curFrame_Lead_2 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_2 < 130)
      requestAnimationFrame(this.drawcurFrame20_Lead_2.bind(this));
  }

  drawcurFrame20_Lead_3() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_Lead_3,
      "1",
      "red",
      this.originX_Lead_3,
      this.originY_Lead_3,
      this.Lead_3,
      this.whichMM
    );
    this.curFrame_Lead_3 += 1; // Frame is incremented one by 1.
    if (this.curFrame_Lead_3 < 130)
      requestAnimationFrame(this.drawcurFrame20_Lead_3.bind(this));
  }
  drawcurFrame20_AVR() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVR,
      "1",
      "red",
      this.originX_AVR,
      this.originY_AVR,
      this.AVR,
      this.whichMM
    );
    this.curFrame_AVR += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVR < 130)
      requestAnimationFrame(this.drawcurFrame20_AVR.bind(this));
  }

  drawcurFrame20_AVL() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVL,
      "1",
      "red",
      this.originX_AVL,
      this.originY_AVL,
      this.AVL,
      this.whichMM
    );
    this.curFrame_AVL += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVL < 130)
      requestAnimationFrame(this.drawcurFrame20_AVL.bind(this));
  }
  drawcurFrame20_AVF() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_AVF,
      "1",
      "red",
      this.originX_AVF,
      this.originY_AVF,
      this.AVF,
      this.whichMM
    );

    this.curFrame_AVF += 1; // Frame is incremented one by 1.
    if (this.curFrame_AVF < 130)
      requestAnimationFrame(this.drawcurFrame20_AVF.bind(this));
  }

  drawcurFrame20_V_1() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_1,
      "1",
      "red",
      this.originX_V_1,
      this.originY_V_1,
      this.V_1,
      this.whichMM
    );

    this.curFrame_V_1 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_1 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_1.bind(this));
  }

  drawcurFrame20_V_2() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_2,
      "1",
      "red",
      this.originX_V_2,
      this.originY_V_2,
      this.V_2,
      this.whichMM
    );
    this.curFrame_V_2 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_2 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_2.bind(this));
  }

  drawcurFrame20_V_3() {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_3,
      "1",
      "red",
      this.originX_V_3,
      this.originY_V_3,
      this.V_3,
      this.whichMM
    );
    this.curFrame_V_3 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_3 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_3.bind(this));
  }

  drawcurFrame20_V_4(V_4: any) {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_4,
      "1",
      "red",
      this.originX_V_4,
      this.originY_V_4,
      this.V_4,
      this.whichMM
    );
    this.curFrame_V_4 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_4 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_4.bind(this));
  }

  drawcurFrame20_V_5(V_5: any) {
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_5,
      "1",
      "red",
      this.originX_V_5,
      this.originY_V_5,
      this.V_5,
      this.whichMM
    );
    this.curFrame_V_5 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_5 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_5.bind(this));
  }

  drawcurFrame20_V_6() {
    console.log("inside ...............")
    this.Draw_Lead(
      this.nPointsPerFrame,
      this.nPointsPerFrame * this.curFrame_V_6,
      "1",
      "red",
      this.originX_V_6,
      this.originY_V_6,
      this.V_6,
      this.whichMM
    );
    this.curFrame_V_6 += 1; // Frame is incremented one by 1.
    if (this.curFrame_V_6 < 130)
      requestAnimationFrame(this.drawcurFrame20_V_6.bind(this));
  }

  plotGraphs() {
    this.prevMMSel = 10;
    console.log("INSIDE PLOTGRAPHS");
    this.drawcurFrame_Lead_1();
    this.drawcurFrame_Lead_2();
    this.drawcurFrame_Lead_3();
    this.drawcurFrame_AVR();
    this.drawcurFrame_AVL();
    this.drawcurFrame_AVF();
    this.drawcurFrame_V_1();
    this.drawcurFrame_V_2();
    this.drawcurFrame_V_3();

    if (this.V_4) this.drawcurFrame_V_4(this.V_4);
    if (this.V_5) this.drawcurFrame_V_5(this.V_5);

    this.drawcurFrame_V_6();
  }


  TestEcgGraph() {
    (this.byId("ecgContent") as HTMLElement).style.display = "block";
    (this.byId("ecgHeader") as HTMLElement).style.display = "block";

    this.createGraphPaper();
    this.plotGraphs();
  }



 
  allPatient: any[] = [];


  testImg = "";
  reportWindow = null;
  printInterval: number | undefined;
EcgReport(event: any) {
  const printElement = document.getElementById("print");
  const canvas = document.getElementById("ecg_canvas") as HTMLCanvasElement;

  if (!printElement || !canvas) {
    console.error("Missing print element or canvas");
    return;
  }

  // Show the print content
  printElement.style.display = "block";

  // Prepare the canvas image
  const image = new Image();
  image.src = canvas.toDataURL("image/png");
  image.width = 1150;
  image.height = 600;
  image.style.border = "3px solid green";
  image.style.marginTop = "20px";

  const htmlContent = `
    <html>
      <head>
        <title>ECG Report</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          table { width: 100%; border-collapse: collapse; }
          td { padding: 6px; }
          .commentText { white-space: pre-wrap; }
        </style>
      </head>
      <body onload="window.print(); window.close();">
        ${printElement.innerHTML}
        ${image.outerHTML}
      </body>
    </html>
  `;

  // Open the popup and write the contents
  const popupWin = window.open("", "_blank", "width=1200,height=800");
  if (popupWin) {
    popupWin.document.open();
    popupWin.document.write(htmlContent);
    popupWin.document.close();
  } else {
    console.error("Popup window could not be opened.");
  }

  // Hide the print section again
  setTimeout(() => {
    printElement.style.display = "none";
  }, 500);
}

  getECGPast() {
    this.ecgContents = true;
    try {
      let encryptedStethoData = this.encryptData(this.first6LeadsData);
      console.log("Encrypted ECG Data:", encryptedStethoData);
      let data = {
        action: "save_ecg_file_Data",
        // domain: this.currDomainId.toString(),
        // consultationId: this.getPatientData.consultationId,
        // patientId: this.getPatientData.patientId.toString(),
        domain: 21,
        consultationId: 87,
        patientId: 8,
        isNewReading: "1",
        isBle: "true",
        ecgPulse: this.pulseData.toString(),
        ecgDataChunk: encryptedStethoData,
        token: this.tokenRequest,
      };
      console.log("API Data Payload:", data);
      this.apiSvc
        .postServiceJson<ECGResponse>(this.constantSvc.APIConfig.DISPLAYAPPOINTMENTS, data)
        .subscribe(
          (res: ECGResponse) => {
            if (res.status === 'success') {
              console.log('ECG Data saved successfully.');
              this.dialogRef.close('closedByUser'); 
            } else {
              console.error('Failed to save ECG Data:', res.message);
            }
          },
          (error) => {
            console.error('API call failed:', error);
          }
        );


    } catch (error) {
      console.error("An error occurred in getECGPast:", error);
    }
  }
  getECGSecondPast() {
    try {
      let encryptedStethoData = this.encryptData(this.second6LeadsData);
      console.log("Encrypted ECG Data:", encryptedStethoData);
      let data = {
        action: "save_ecg_file_Data",
        // domain: this.currDomainId.toString(),
        // consultationId: this.getPatientData.consultationId,
        // patientId: this.getPatientData.patientId.toString(),
        domain: 21,
        consultationId: 87,
        patientId: 8,
        isNewReading: "0",
        isBle: "true",
        ecgPulse: this.pulseData.toString(),
        ecgDataChunk: encryptedStethoData,
        token: this.tokenRequest,
      };
      console.log("API Data Payload:", data);
      this.apiSvc
        .postServiceJson<ECGResponse>(this.constantSvc.APIConfig.DISPLAYAPPOINTMENTS, data)
        .subscribe(
          (res: ECGResponse) => {
            if (res.status === 'success') {
              console.log('ECG Data saved successfully.');
            } else {
              console.error('Failed to save ECG Data:', res.message);
            }
          },
          (error) => {
            console.error('API call failed:', error);
          }
        );

    } catch (error) {
      console.error("An error occurred in getECGPast:", error);
    }
  }


  encryptData(data: any): string {
    try {
      // Serialize data if it's not a string
      const dataString = typeof data === "string" ? data : JSON.stringify(data);

      // Log the length of the dataString
      console.log("dataString Length:", dataString + dataString.length);

      const key = CryptoJS.enc.Hex.parse(this.secretKey); // Ensure this.secretKey is valid

      const encrypted = CryptoJS.AES.encrypt(dataString, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      });

      console.log("encrypted", encrypted);

      // Convert the ciphertext to hex and return
      return CryptoJS.enc.Hex.stringify(encrypted.ciphertext);
    } catch (error) {
      console.error("Encryption failed:", error);
      throw new Error("Data encryption failed.");
    }
  }

}
