import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar, IonButton, IonItem, IonLabel,IonButtons } from '@ionic/angular/standalone';
import { NovaIcareLauncher } from 'src/app/core/plugin/nova-icare-launcher';
import { PluginListenerHandle } from '@capacitor/core';
import { MedicalDeviceCommunicationPlugin } from 'src/app/core/plugins/medical-device-communication.plugin';
import { DeviceType } from 'src/app/core/interfaces/medical-device.interface';
import { TranslateModule } from '@ngx-translate/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-icare-rapid-test',
  templateUrl: './icare-rapid-test.page.html',
  styleUrls: ['./icare-rapid-test.page.scss'],
  standalone: true,
  imports: [TranslateModule,IonContent, IonHeader, IonTitle, IonToolbar, IonButton, IonItem, IonLabel, CommonModule, FormsModule,IonButtons]
})
export class IcareRapidTestPage implements OnInit, OnDestroy {

  testResult: string = '';
  testName: string = '';
  imageName: string = '';
  capturedImage: string = '';
  showResults: boolean = false;
  
  // Selected test type - start with empty to force user selection
  selectedTestType: string = '';

  // Available test types
  testTypes = [
    { value: 'HEPA_SCAN_HBsAg', label: 'Hepatitis B Surface Antigen (HBsAg)' },
    { value: 'HEPA_SCAN_HCV', label: 'Hepatitis C Virus (HCV)' },
    { value: 'SYPHILIS', label: 'Syphilis' },
    { value: 'TROPONIN_I', label: 'Troponin I' },
    { value: 'MALERIA_P.f_P.v', label: 'Malaria P.f/P.v' },
    { value: 'MALERIA_P.f_PAN', label: 'Malaria P.f/PAN' },
    { value: 'DENGUE_IgG_IgM', label: 'Dengue IgG/IgM' },
    { value: 'DENGUE_NS1', label: 'Dengue NS1' },
    { value: 'PREGNANCY_HCG', label: 'Pregnancy HCG' },
    { value: 'HIV_TRILINE', label: 'HIV Triline' }
  ];

  // Store listener handles for cleanup
  private listenerHandles: PluginListenerHandle[] = [];

  constructor(

     private dialogRef: MatDialogRef<IcareRapidTestPage>

  ) { }

  ngOnInit() {
    this.setupEventListeners();
    console.log('🔧 Test types loaded:', this.testTypes);
    console.log('🔧 Initial selected type:', this.selectedTestType);
  }

  ngOnDestroy() {
    // Clean up all listeners
    this.listenerHandles.forEach(handle => handle.remove());
  }

  private async setupEventListeners() {
    try {
      console.log('🔧 Setting up event listeners for both plugins...');

      // ✅ NovaIcareLauncher listeners (your existing working listeners)
      const novaListener1 = await NovaIcareLauncher.addListener('opticalreaderlis', (data: any) => {
        console.log('🎯 NovaIcareLauncher - opticalreaderlis event:', JSON.stringify(data, null, 2));
        this.handleOpticalReaderResult(data, 'NovaIcareLauncher');
      });

      // ✅ MedicalDeviceCommunicationPlugin listeners (NEW - this fixes the "No listeners found")
      const medicalListener1 = await MedicalDeviceCommunicationPlugin.addListener('opticalreaderlis', (data: any) => {
        console.log('🎯 MedicalDevicePlugin - opticalreaderlis event:', JSON.stringify(data, null, 2));
        this.handleOpticalReaderResult(data, 'MedicalDevicePlugin');
      });

      const medicalListener2 = await MedicalDeviceCommunicationPlugin.addListener('DeviceResult', (data: any) => {
        console.log('🎯 MedicalDevicePlugin - DeviceResult event:', JSON.stringify(data, null, 2));
       if (data.deviceType === 'optical_reader' || data.deviceType === 'OPTICAL_READER') {
          //this.handleOpticalReaderResult(data, 'MedicalDevicePlugin-DeviceResult');
          console.log(' lisner already called so stoping this lisner here itself');
        }
      });

      // Store all listener handles for cleanup
      this.listenerHandles.push(novaListener1, medicalListener1, medicalListener2);

      console.log('✅ All event listeners registered successfully');

    } catch (error) {
      console.error('❌ Failed to setup event listeners:', error);
    }
  }

  private handleOpticalReaderResult(data: any, source: string) {
    console.log(`📊 Processing optical reader result from ${source}:`, JSON.stringify(data, null, 2));

    // Handle different data structures from both plugins
    let actualData = data;

    // MedicalDevicePlugin sends data nested in 'data' property
    if (data.data && typeof data.data === 'object') {
      actualData = data.data;
      console.log('📊 Using nested data from MedicalDevicePlugin:', JSON.stringify(actualData, null, 2));
    }

    // Extract test results - handle both camelCase and snake_case
    this.testResult = actualData.testResult || actualData.test_result || 'No result available';
    this.testName = actualData.testName || actualData.test_name || 'Unknown test';
    this.imageName = actualData.imageName || actualData.image_name || 'No image name';

    console.log(`📊 Extracted from ${source}:`, JSON.stringify({
      testResult: this.testResult,
      testName: this.testName,
      imageName: this.imageName
    }, null, 2));

    // Handle image data - check both possible property names
    if (actualData.imageFileUri || actualData.image_file_uri) {
      const imageData = actualData.imageFileUri || actualData.image_file_uri;
      this.capturedImage = `data:image/jpeg;base64,${imageData}`;
      console.log('✅ Base64 image data processed from:', source);
      console.log('📷 Image data length:', imageData?.length || 'undefined');
    } else if (actualData.opticalImagePath || actualData.optical_image_path) {
      const imagePath = actualData.opticalImagePath || actualData.optical_image_path;
      console.log('📁 Image path received from:', source, imagePath);
      this.capturedImage = imagePath;
    } else {
      console.log('⚠️ No image data found from:', source);
      console.log('⚠️ Available keys:', Object.keys(actualData));
    }

    // Show results
    this.showResults = true;
    console.log(`✅ UI updated with results from ${source}`);
  }

  async launchRapidtest() {
    console.log('🚀 Launching iCare rapid test with test type:', this.selectedTestType);
    
    try {
      // Clear previous results
      this.showResults = false;
      this.testResult = '';
      this.testName = '';
      this.imageName = '';
      this.capturedImage = '';

      const res = await NovaIcareLauncher.launchDeviceWithResult({
        class_name: 'io.ionic.starter.MainActivity',
        package_name: 'io.ionic.starter',
        language: 'en',
        patient_id: '12345',
        real_id: '',
        deviceType: DeviceType.OPTICAL_READER,
        testType: this.selectedTestType  // Pass selected test type
      });

      console.log('✅ Launch success:', JSON.stringify(res, null, 2));

      // Both plugins should now send events when results come back
      console.log('⏳ Waiting for events from both plugins...');

    } catch (err) {
      console.error('❌ Launch failed:', err);
      alert(`Launch failed: ${err}`);
    }
  }

  onImageLoad() {
    console.log('✅ Image loaded successfully');
  }

  onImageError() {
    console.error('❌ Failed to load image');
  }

  // Helper method to get test type label
  getTestTypeLabel(testTypeValue: string): string {
    const testType = this.testTypes.find(t => t.value === testTypeValue);
    return testType ? testType.label : testTypeValue;
  }

  // Handle native select change - ONLY method needed for dropdown
  onTestTypeChange(event: any) {
    this.selectedTestType = event.target.value;
    
    // Clear/close previous results when a new test is selected
    this.showResults = false;
    this.testResult = '';
    this.testName = '';
    this.imageName = '';
    this.capturedImage = '';
    
    console.log('✅ Test type selected:', this.selectedTestType);
    console.log('✅ Test type label:', this.getTestTypeLabel(this.selectedTestType));
    console.log('🧹 Previous results cleared for new test selection');
  }

   closeDialog() {
    this.dialogRef.close();
  }
}