import { Routes } from '@angular/router';
import { AuthGuard } from './core/auth/auth.guard';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () =>
      import('./home/<USER>').then(m => m.HomePage),
  },

  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'login',
    loadComponent: () => import('./modules/login/login/login.page').then( m => m.LoginPage)
  },
  {
    path: 'forgot-password',
    loadComponent: () => import('./modules/login/forgot-password/forgot-password.page').then( m => m.ForgotPasswordPage)
  },
  {
    path: 'thermometer',
    loadComponent: () => import('./modules/prms/ble-device/thermometer/thermometer.page').then( m => m.ThermometerPage)
  },
  {
    path: 'parameter',
    loadComponent: () => import('./modules/prms/parameter/parameter.page').then( m => m.ParameterPage)
  },
  {
    path: 'spo2-device',
    loadComponent: () => import('./modules/prms/ble-device/spo2-device/spo2-device.page').then( m => m.Spo2DevicePage)
  },
  {
    path: 'common-dialog',
    loadComponent: () => import('./modules/common/common-dialog/common-dialog.page').then( m => m.CommonDialogPage)
  },
  {
    path: 'icare-device',
    loadComponent: () => import('./modules/prms/ble-device/icare-device/icare-device.page').then( m => m.IcareDevicePage)
  },
  {
    path: 'spo2-manual-entry',
    loadComponent: () => import('./modules/prms/ble-device/spo2-manual-entry/spo2-manual-entry.page').then( m => m.Spo2ManualEntryPage)
  },
  {
    path: 'icare-spo2',
    loadComponent: () => import('./modules/prms/ble-device/icare-spo2/icare-spo2.page').then( m => m.IcareSpo2Page)
  },
  {
    path: 'icare-thermometer',
    loadComponent: () => import('./modules/prms/ble-device/icare-thermometer/icare-thermometer.page').then( m => m.IcareThermometerPage)
  },
  {
    path: 'rapid-test',
    loadComponent: () => import('./modules/prms/usb-device/rapid-test/rapid-test.page').then( m => m.RapidTestPage)
  },
  {
    path: 'icare-rapid-test',
    loadComponent: () => import('./modules/prms/usb-device/icare-rapid-test/icare-rapid-test.page').then( m => m.IcareRapidTestPage)
  },
  {
    path: 'hemoglobin',
    loadComponent: () => import('./modules/prms/usb-device/hemoglobin/hemoglobin.page').then( m => m.HemoglobinPage)
  },
  {
    path: 'ecg-jetty-device',
    loadComponent: () => import('./modules/prms/ble-device/ecg-jetty-device/ecg-jetty-device.page').then( m => m.EcgJettyDevicePage)
  },
  {
    path: 'ecg-icare-device',
    loadComponent: () => import('./modules/prms/ble-device/ecg-icare-device/ecg-icare-device.page').then( m => m.EcgIcareDevicePage)
  },
  {
    path: 'stethoscope-device',
    loadComponent: () => import('./modules/prms/ble-device/stethoscope-device/stethoscope-device.page').then( m => m.StethoscopeDevicePage)
  },


];
  
  
  