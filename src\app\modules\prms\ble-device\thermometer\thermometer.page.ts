import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  AfterViewInit,
  Inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { WebsocketsService } from 'src/app/core/services/websocket.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import * as d3 from 'd3';
import * as $ from 'jquery';

@Component({
  selector: 'app-thermometer',
  templateUrl: './thermometer.page.html',
  styleUrls: ['./thermometer.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, MatTooltipModule],
})
export class ThermometerPage implements OnInit, AfterViewInit {
  @ViewChild('thermo', { static: true }) thermo!: ElementRef;
  @ViewChild('thermofan', { static: true }) thermofan!: ElementRef;

  temperatureVal = 33.9; // °C
  temperatureFVal = 93;  // °F
  battery = 0;
  valSubscription!: Subscription;
  batteryColor: string = '#8deb09';

  private width = 180;
  private height = 360;
  private bulbRadius = 36;
  private tubeWidth = 30;
  private tubeBorderWidth = 4;
  private mercuryColor = '#0099ff';
  private tubeBorderColor = 'lightblue';

  constructor(
    private modalCtrl: ModalController,
    private websocketsService: WebsocketsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<any>
  ) { }

  ngOnInit(): void {
    this.valSubscription = this.websocketsService.bleValueObs$.subscribe(data => {
      if (data.match(/setTemperature/g) && !data.match(/ReadingCompleted/g)) {
        const tempF = Number(data.split("~")[1]);
        this.temperatureFVal = Math.round((tempF + Number.EPSILON) * 100) / 100;
        this.temperatureVal = Math.round(((tempF - 32) * 5 / 9 + Number.EPSILON) * 100) / 100;
        d3.selectAll("svg").remove();
        this.draw();
      }
      if (data.match(/changeBatteryStatus/g)) {
        var batteryBit = Number(data.split("~")[1]);
        if (batteryBit < 4) {
          this.battery = 10;
          this.batteryColor = 'red';
        } else if (batteryBit == 4) {
          this.battery = 33;
          this.batteryColor = 'orange';
        } else if (batteryBit == 5) {
          this.battery = 66;
          this.batteryColor = '#8deb09';
        } else if (batteryBit == 6) {
          this.battery = 100;
          this.batteryColor = '#8deb09';
        }
      }

    });
  }

  ngAfterViewInit(): void {
    this.draw();
  }

  draw(): void {
    this.drawThermometer(this.thermo.nativeElement, 26.7, 43.3, this.temperatureVal, '°C', 'celsius');
    this.drawThermometer(this.thermofan.nativeElement, 80, 110, this.temperatureFVal, '°F', 'fahrenheit');
  }


private drawThermometer(
  container: HTMLElement,
  minTemp: number,
  maxTemp: number,
  currentTemp: number,
  unit: string,
  id: string
): void {
  const marginLeft = 60;
  const axisOffset = 40;
  const tickLineLength = 15;

  const svg = d3.select(container)
    .append('svg')
    .attr('id', `svg-${id}`)
    .attr('width', this.width + marginLeft + axisOffset)
    .attr('height', this.height + 60);

  const defs = svg.append('defs');

  const tubeGradient = defs.append('linearGradient')
    .attr('id', `tubeGradient-${id}`)
    .attr('x1', '0%').attr('y1', '0%').attr('x2', '100%').attr('y2', '0%');
  tubeGradient.append('stop').attr('offset', '0%').attr('stop-color', '#f8f9fa');
  tubeGradient.append('stop').attr('offset', '50%').attr('stop-color', '#ffffff');
  tubeGradient.append('stop').attr('offset', '100%').attr('stop-color', '#e9ecef');

  const mercuryGradient = defs.append('linearGradient')
    .attr('id', `mercuryGradient-${id}`)
    .attr('x1', '0%').attr('y1', '0%').attr('x2', '100%').attr('y2', '0%');
  mercuryGradient.append('stop').attr('offset', '0%').attr('stop-color', '#00b4d8');
  mercuryGradient.append('stop').attr('offset', '50%').attr('stop-color', '#00b4d8');
  mercuryGradient.append('stop').attr('offset', '100%').attr('stop-color', '#00b4d8');

  const filter = defs.append('filter')
    .attr('id', `shadow-${id}`)
    .attr('x', '-50%').attr('y', '-50%').attr('width', '200%').attr('height', '200%');
  filter.append('feDropShadow')
    .attr('dx', 2).attr('dy', 4).attr('stdDeviation', 3).attr('flood-color', 'rgba(0,0,0,0.3)');

  const innerShadow = defs.append('filter')
    .attr('id', `innerShadow-${id}`)
    .attr('x', '-50%').attr('y', '-50%').attr('width', '200%').attr('height', '200%');
  innerShadow.append('feOffset').attr('dx', 0).attr('dy', 2);
  innerShadow.append('feGaussianBlur').attr('stdDeviation', 2).attr('result', 'offset-blur');
  innerShadow.append('feFlood').attr('flood-color', 'rgba(0,0,0,0.2)');
  innerShadow.append('feComposite').attr('in2', 'offset-blur').attr('operator', 'in');

  const bulb_cx = marginLeft + this.width / 2;
  const bottomY = this.height + 45;
  const bulb_cy = bottomY - this.bulbRadius;
  const top_cy = 30 + this.tubeWidth / 2; // ⬅️ shifted down from 30 to 50

  const scale = d3.scaleLinear()
    .range([bulb_cy + 5, top_cy])
    .domain([minTemp, maxTemp]);

  // Outer tube
  svg.append('rect')
    .attr('x', bulb_cx - this.tubeWidth / 2)
    .attr('y', top_cy)
    .attr('width', this.tubeWidth)
    .attr('height', bulb_cy - top_cy)
    .attr('rx', this.tubeWidth / 2)
    .attr('ry', this.tubeWidth / 2)
    .style('fill', `url(#tubeGradient-${id})`)
    .style('stroke', '#adb5bd')
    .style('stroke-width', '2px')
    .style('filter', `url(#shadow-${id})`);

  // Inner tube
  svg.append('rect')
    .attr('x', bulb_cx - (this.tubeWidth - 6) / 2)
    .attr('y', top_cy + 3)
    .attr('width', this.tubeWidth - 6)
    .attr('height', bulb_cy - top_cy - 6)
    .attr('rx', (this.tubeWidth - 6) / 2)
    .attr('ry', (this.tubeWidth - 6) / 2)
    .style('fill', '#f8f9fa')
    .style('stroke', '#dee2e6')
    .style('stroke-width', '1px')
    .style('filter', `url(#innerShadow-${id})`);

  // Mercury fill
  svg.append('rect')
    .attr('id', `mercury-${id}`)
    .attr('x', bulb_cx - (this.tubeWidth - 10) / 2)
    .attr('y', scale(currentTemp))
    .attr('width', this.tubeWidth - 10)
    .attr('height', bulb_cy - scale(currentTemp))
    .attr('rx', (this.tubeWidth - 10) / 2)
    .attr('ry', (this.tubeWidth - 10) / 2)
    .style('fill', `url(#mercuryGradient-${id})`)
    .style('filter', `url(#shadow-${id})`);

  svg.append('rect')
    .attr('x', bulb_cx - (this.tubeWidth - 14) / 2)
    .attr('y', scale(currentTemp))
    .attr('width', 4)
    .attr('height', Math.max(0, bulb_cy - scale(currentTemp) - 5))
    .attr('rx', 2)
    .attr('ry', 2)
    .style('fill', 'rgba(255,255,255,0.4)')
    .style('opacity', 0.7);

  // Bulb visuals
  svg.append('circle')
    .attr('r', this.bulbRadius)
    .attr('cx', bulb_cx)
    .attr('cy', bulb_cy)
    .style('fill', `url(#tubeGradient-${id})`)
    .style('stroke', '#adb5bd')
    .style('stroke-width', '2px')
    .style('filter', `url(#shadow-${id})`);

  svg.append('circle')
    .attr('r', this.bulbRadius - 3)
    .attr('cx', bulb_cx)
    .attr('cy', bulb_cy)
    .style('fill', '#f8f9fa')
    .style('stroke', '#dee2e6')
    .style('stroke-width', '1px')
    .style('filter', `url(#innerShadow-${id})`);

  svg.append('circle')
    .attr('r', this.bulbRadius - 6)
    .attr('cx', bulb_cx)
    .attr('cy', bulb_cy)
    .style('fill', `url(#mercuryGradient-${id})`)
    .style('filter', `url(#shadow-${id})`);

  svg.append('circle')
    .attr('r', this.bulbRadius - 10)
    .attr('cx', bulb_cx - 6)
    .attr('cy', bulb_cy - 6)
    .style('fill', 'rgba(255,255,255,0.3)')
    .style('opacity', 0.8);

  svg.append('text')
    .attr('x', bulb_cx)
    .attr('y', bulb_cy + 8)
    .attr('text-anchor', 'middle')
    .style('font-family', 'Arial, sans-serif')
    .style('font-size', '14px')
    .style('font-weight', 'bold')
    .style('fill', 'white')
    .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.5)')
    .text(`${currentTemp.toFixed(1)}${unit}`);

  let tickValues: number[] = [];
  if (unit === '°F') {
    tickValues = d3.range(minTemp, maxTemp + 1, 2);
  } else {
    for (let val = 26.7; val <= 43.3; val = Math.round((val + 1.1) * 10) / 10) {
      tickValues.push(val);
    }
  }

  const axis = d3.axisLeft(scale)
    .tickValues(tickValues)
    .tickFormat(unit === '°F' ? d3.format('d') : d3.format('.1f'));

  const axisGroup = svg.append('g')
    .attr('transform', `translate(${bulb_cx - this.tubeWidth / 2 - tickLineLength - 5}, 20)`)
    .call(axis);

  const boldCelsius = this.data?.boldCelsius ?? [26.7, 32.2, 37.7, 43.2];
  const boldFahrenheit = this.data?.boldFahrenheit ?? [80, 90, 100, 110];

  axisGroup.selectAll('text')
    .style('font-family', 'Arial, sans-serif')
    .style('fill', (d: any) => {
      const val = Number(d.toFixed(1));
      return (
        (id === 'celsius' && boldCelsius.includes(val)) ||
        (id === 'fahrenheit' && boldFahrenheit.includes(val))
      ) ? '#2c3e50' : '#495057';
    })
    .style('font-size', (d: any) => {
      const val = Number(d.toFixed(1));
      return (
        (id === 'celsius' && boldCelsius.includes(val)) ||
        (id === 'fahrenheit' && boldFahrenheit.includes(val))
      ) ? '12px' : '12px';
    })
    .style('font-weight', (d: any) => {
      const val = Number(d.toFixed(1));
      return (
        (id === 'celsius' && boldCelsius.includes(val)) ||
        (id === 'fahrenheit' && boldFahrenheit.includes(val))
      ) ? 'normal' : 'normal';
    })
    .attr('text-anchor', 'end')
    .attr('dx', (d: any) => {
      const val = Number(d.toFixed(1));
      if ((id === 'fahrenheit' && val === 80) || (id === 'celsius' && val === 26.7)) {
        return '-16';
      } else if ((id === 'fahrenheit' && val <= 84) || (id === 'celsius' && val <= 28.9)) {
        return '-12';
      }
      return '-8';
    });

// First, style all lines normally
axisGroup.selectAll('line')
  .style('stroke', '#495057')
  .style('stroke-width', '1px')
  .style('opacity', 0.8)
  .attr('x2', tickLineLength);

// Then draw bold lines separately (they will appear on top)
axisGroup.selectAll('line')
  .filter((d: any) => {
    const val = Number(d.toFixed(1));
    return (id === 'celsius' && boldCelsius.includes(val)) ||
           (id === 'fahrenheit' && boldFahrenheit.includes(val));
  })
  .style('stroke', '#2c3e50') // Darker color for bold lines
  .style('stroke-width', '2.5px')
  .style('opacity', 1)
  .attr('x2', tickLineLength)
  .raise(); // This brings them to the front


  axisGroup.select('path').remove();

  svg.insert('rect', ':first-child')
    .attr('x', 0)
    .attr('y', 0)
    .attr('width', this.width + marginLeft + axisOffset)
    .attr('height', this.height + 60)
    .attr('rx', 10)
    .attr('ry', 10)
    .style('fill', 'rgba(248, 249, 250, 0.5)')
    .style('stroke', 'rgba(173, 181, 189, 0.3)')
    .style('stroke-width', '1px');
}





}
