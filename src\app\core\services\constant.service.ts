import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ConstantService {
  constructor() {}

 public APIConfig = {
    API_BASE: environment.APIBaseURL,  
  LOGIN: environment.APIBaseURL + 'UserRegistrationAPI.do?action=loginservices',
  LOGOUT: environment.APIBaseURL + 'AndroidCommonServices.do?action=logoutservices',
  GETDOCTORSLOTS: environment.APIBaseURL + 'AndroidDoctorServices.do',
  GETAPPOINTMENTS: environment.APIBaseURL + 'pages/FixAppointment',
  SEARCHPATIENTS: environment.APIBaseURL + 'pages/SearchPatient',
  DISPLAYAPPOINTMENTS: environment.APIBaseURL + 'RemediNovaDoctorAPI.do',
  SEARCHPATIENT: environment.APIBaseURL + 'RemediNovaDoctorAPI.do',
// GETCOMMONSERVICES: environment.APIBaseURL + 'RemediNovaDoctorAPI.do',
GETCOMMONSERVICES: 'RemediNovaDoctorAPI.do',
  PATIENTCOMPL: environment.APIBaseURL + 'RemediPatientPortalAPI.do',
  PATIENTHISTORY: environment.APIBaseURL + 'RemediNovaDoctorAPI.do',
  VIDEOAPI: environment.APIBaseURL + 'webrtc/HelloWorld.jsp',
  WEBRTCVIDEOSERVLET: environment.APIBaseURL + 'WebRTCVideoServlet.do',
  FORGOTPASSWORD: ''
};

}
