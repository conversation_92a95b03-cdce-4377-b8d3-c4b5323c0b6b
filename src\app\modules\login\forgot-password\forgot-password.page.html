<div class="login-container">
    <div class="login-box">
        <div class="veli_title">Forgot Password</div>

        <!-- Step 1: Send OTP -->
        <form [formGroup]="validateNumberForm" *ngIf="!showOtpSection && !showPasswordSection">
            <div class="input-group">
                <input type="text" formControlName="mobileNum" placeholder="Enter Username" /> @if (submitted && fc['mobileNum'].errors?.['required']) {
                <div class="error">Username is required</div>
                } @if (submitted && fc['mobileNum'].errors?.['pattern']) {
                <div class="error">Only letters and numbers are allowed</div>
                }
            </div>
            <button type="button" class="login-btn" (click)="sendOtp()" [disabled]="sendingOtp">
            {{ sendingOtp ? 'Sending...' : 'Send OTP' }}
            </button>

        </form>

        <!-- Step 2: Verify OTP -->
        <form [formGroup]="verifyOTPForm" *ngIf="showOtpSection && !showPasswordSection">
            <div class="input-group">
                <input type="text" [value]="validateNumberForm.value.mobileNum" disabled />
                <input type="text" formControlName="otp" placeholder="Enter OTP" /> @if (submitted && fcOtp['otp'].errors?.['required']) {
                <div class="error">OTP is required</div>
                }
            </div>
            <button type="button" class="login-btn" (click)="verifyOtp()">Verify OTP</button>
        </form>

        <!-- Step 3: Change Password -->
        <form [formGroup]="passwordForm" *ngIf="showPasswordSection">
            <div class="input-group">
                <input type="password" formControlName="newPassword" placeholder="New Password" /> @if (submitted && fcPass['newPassword'].errors?.['required']) {
                <div class="error">New Password is required</div>
                } @if (submitted && fcPass['newPassword'].errors?.['minlength']) {
                <div class="error">Minimum 8 characters required</div>
                }
            </div>
            <div class="input-group">
                <input type="password" formControlName="confirmPassword" placeholder="Confirm Password" /> @if (submitted && fcPass['confirmPassword'].errors?.['required']) {
                <div class="error">Confirm Password is required</div>
                }
            </div>
            <button type="button" class="login-btn" (click)="changePassword()">Change Password</button>
        </form>
    </div>
</div>