import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  On<PERSON><PERSON>roy,
  AfterViewInit,
  Inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import * as Highcharts from 'highcharts/highstock';
import { WebsocketsService } from 'src/app/core/services/websocket.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-stethoscope-device',
  templateUrl: './stethoscope-device.page.html',
  styleUrls: ['./stethoscope-device.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, MatTooltipModule],
})
export class StethoscopeDevicePage implements OnInit, <PERSON><PERSON><PERSON>roy {
  @ViewChild('stethoScopeGraph', { static: false }) stethoScopeGraphRef!: ElementRef;

  stethoScopehighchart: any = Highcharts;
  battery = 0;
  batteryColor: string = '#8deb09';
  StethosopeDeviceVal: any = [];

  stethoScopeChart: any;
  stethoScopeSeries: any;
  stethoSetScrollFlag: number = 0;
  stethoData: number[] = [];

  StethoInterval: any;
  isBLE: any;
  stethoMulFactor = 1;

  valSubscription: Subscription | null = null;

  constructor(
    private modalCtrl: ModalController,
    private websocketsService: WebsocketsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<any>
  ) {}

  ngOnInit() {
    this.initializeChart();

    this.valSubscription = this.websocketsService.bleValueObs$.subscribe((data) => {
      console.log(data, 'data from setho');

      if (data.match(/plotStethoGraph/g)) {
        const json_data = data.split('~')[1];
        this.StethosopeDeviceVal = JSON.parse(json_data);
        this.plotStethosopeGraph(json_data);
        this.calculateStetho(this.StethosopeDeviceVal);
      }

      if (data.match(/changeBatteryStatus/g)) {
        const batteryBit = Number(data.split('~')[1]);
        if (batteryBit < 4) {
          this.battery = 10;
          this.batteryColor = 'red';
        } else if (batteryBit === 4) {
          this.battery = 33;
          this.batteryColor = 'orange';
        } else if (batteryBit === 5) {
          this.battery = 66;
          this.batteryColor = '#8deb09';
        } else if (batteryBit === 6) {
          this.battery = 100;
          this.batteryColor = '#8deb09';
        }
      }
    });
  }

 initializeChart() {
  const that = this;

  this.stethoScopeChart = Highcharts.chart('stethoScopeGraph', {
    chart: {
      type: 'spline',
      events: {
        load: function () {
          that.stethoScopeSeries = this.series[0];
        }
      }
    },
    title: {
      text: 'Stetho graph',
    },
    xAxis: {
      tickPixelInterval: 100,
      min: 0,
      max: 4000,
    },
    yAxis: {
      tickInterval: 5000,
      title: {
        text: 'Stetho data',
      },
    },
    legend: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    series: [{
      type: 'spline',         // ✅ Required field to fix the error
      color: 'green',
      data: [] as number[],   // Optional: Type hint to avoid TS confusion
    }],
    scrollbar: {
      enabled: true,
      barBackgroundColor: 'gray',
      barBorderRadius: 7,
      barBorderWidth: 0,
      buttonBackgroundColor: 'gray',
      buttonBorderWidth: 0,
      buttonArrowColor: 'yellow',
      buttonBorderRadius: 7,
      rifleColor: 'yellow',
      trackBackgroundColor: 'white',
      trackBorderWidth: 1,
      trackBorderColor: 'silver',
      trackBorderRadius: 7,
    }
  });
}


  plotStethosopeGraph(data: string) {
    const arr = JSON.parse(data);
    const skippedArr = [];

    for (let i = 0; i < arr.length; i += 2) {
      skippedArr.push(arr[i]);
    }

    this.stethoData = this.stethoData.concat(skippedArr);

    if (this.stethoScopeSeries) {
      this.stethoScopeSeries.setData(this.stethoData, true, false, false);
    }

    if (this.stethoSetScrollFlag >= 3) {
      const xMax = this.stethoScopeChart.xAxis[0].max;
      this.stethoScopeChart.xAxis[0].setExtremes(xMax, xMax + 2000);
    }
    this.stethoSetScrollFlag++;
  }

  calculateStetho(data: any[]) {
    const bdataLength = 30 * 8000;
    let stetho_1secdata: number[] = [];

    for (let i = 0; i < data.length; i++) {
      const sample = data[i];
      const temp = sample * this.stethoMulFactor;
      stetho_1secdata.push(temp);

      if (stetho_1secdata.length === 4000) {
        const temp1 = [...stetho_1secdata];
        // this.PlayStethoSound(temp1);
        stetho_1secdata.length = 0;
      }
    }
  }

  ngOnDestroy() {
    if (this.valSubscription) {
      this.valSubscription.unsubscribe();
    }
  }

  onDialogClose(): void {
    this.dialogRef.close();
  }
}
