// Type definitions for patient data
export interface PatientData {
  _id?: string;
  _rev?: string;
  domainwisepid: number;
  patientid: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  age: string;
  ageYears: string;
  gender: string;
  maritalstatus: string;
  height: string;
  weight: string;
  mobile: string;
  email: string;
  head_of_household_fname: string;
  head_of_household_lname: string;
  country: string;
  state: string;
  district: string;
  block: string;
  village: string;
  address: string;
  projid: string;
  head_of_household_mobile: string;
  isAbhaPatient: boolean;
  profile: {
    patientid: number;
    imagepath: string;
    S3URL: string;
  };
  pastrecord: any;
  createdat: string;
  createdby: string;
  domain: number;
  uid: string;
  prefix: any;
  EhealthId: string;
  MRN: string;
  password: string;
  consentformcheckstatus: number;
  fingerPrintTemplate: string;
  health_number: string;
  health_address: string;
  unique_id: any;
  nationalId: any;
  ethnicity: any;
  subscriptionDetails: {
    subscribedId: number;
    familycardid: any;
    freeSubcriptionAllocated: number;
    completedFreeSubcrition: number;
    remainingSubcription: number;
    isActive: any;
    subcriptionName: any;
    subscriptionPlanActivatedOn: any;
    subscriptionExpiredOn: any;
    isExpaired: number;
  };
  localId: string;
  patient_status: any;
  patient_title: any;
  postCode: any;
  centerName: any;
  status: any;
  isSync: boolean;
  documents: Document[];
  type: string;
}

export interface Document {
  id: number;
  patientid: number;
  fileName: string;
  fileType: string;
  data?: string;
  imagepath: string;
  S3URL: string;
  type: string;
}

export interface MasterData {
  tblcountry?: any[];
  tblstate?: any[];
  tbldistrict?: any[];
  tblblock?: any[];
  tblvillage?: any[];
}
