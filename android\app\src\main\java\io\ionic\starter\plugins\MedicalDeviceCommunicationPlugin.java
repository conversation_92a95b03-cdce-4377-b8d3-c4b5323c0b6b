package io.ionic.starter.plugins;

import android.content.Intent;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;

import io.ionic.starter.services.NovaICareIntentService;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@CapacitorPlugin(name = "MedicalDeviceCommunicationPlugin")
public class MedicalDeviceCommunicationPlugin extends Plugin {

    private static final String TAG = "MedicalDevicePlugin";

    public static MedicalDeviceCommunicationPlugin instance;
    private NovaICareIntentService intentService;
    private Map<String, PluginCall> pendingCalls = new HashMap<>();

    @Override
    public void load() {
        instance = this;
        intentService = new NovaICareIntentService(getContext());
        Log.d(TAG, "✅ Medical Device Communication Plugin loaded");
    }

    public void handleDeviceResultManually(int requestCode, int resultCode, Intent data) {
        PluginCall call = null;
        // Find the matching PluginCall from pendingCalls if needed
        // For simplicity, use the first pending call
        if (!pendingCalls.isEmpty()) {
            call = pendingCalls.values().iterator().next();
        }
        if (call != null) {
            handleDeviceResult(call, resultCode, data);
            pendingCalls.remove(call);
        } else {
            Log.w(TAG, "No pending PluginCall found for manual device result handling");
        }
    }

    @PluginMethod
    public void launchDevice(PluginCall call) {
        try {
            Log.d(TAG, "🚀 Launching medical device");

            String deviceType = call.getString("deviceType", "HEMOGLOBIN");
            String packageName = call.getString("packageName", "");
            String className = call.getString("className", "");
            String activityName = call.getString("activityName", "");
            String language = call.getString("language", "en");
            String patientId = call.getString("patientId", "");
            String realId = call.getString("realId", "");
            String sessionId = call.getString("sessionId", "");

            if (packageName.isEmpty() || patientId.isEmpty()) {
                call.reject("Missing required parameters: packageName or patientId");
                return;
            }

            if (!intentService.isNovaICareInstalled()) {
                call.reject("NovaICare app is not installed");
                return;
            }

            if (!intentService.isDeviceActivityAvailable(deviceType)) {
                call.reject("Device type not supported: " + deviceType);
                return;
            }

            Map<String, Object> additionalExtras = new HashMap<>();
            JSObject extras = call.getObject("extras");
            if (extras != null) {
                Iterator<String> keys = extras.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    additionalExtras.put(key, extras.get(key));
                }
            }

            NovaICareIntentService.DeviceLaunchRequest request =
                    new NovaICareIntentService.DeviceLaunchRequest(
                            deviceType, className, packageName, language,
                            realId.isEmpty() ? patientId : realId, sessionId, additionalExtras
                    );

            Intent intent = intentService.createDeviceIntent(request);
            String callId = generateCallId();
            pendingCalls.put(callId, call);

            startActivityForResult(call, intent, "handleDeviceResult");

            Log.d(TAG, "✅ Device launch initiated: " + deviceType);

        } catch (NovaICareIntentService.IntentCreationException e) {
            Log.e(TAG, "❌ Intent creation failed", e);
            call.reject("Failed to create intent: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "❌ Device launch failed", e);
            call.reject("Device launch failed: " + e.getMessage());
        }
    }

    @PluginMethod
    public void isDeviceAvailable(PluginCall call) {
        try {
            String deviceType = call.getString("deviceType", "HEMOGLOBIN");

            boolean isInstalled = intentService.isNovaICareInstalled();
            boolean isDeviceAvailable = isInstalled && intentService.isDeviceActivityAvailable(deviceType);

            JSObject result = new JSObject();
            result.put("available", isDeviceAvailable);
            result.put("novaICareInstalled", isInstalled);
            result.put("deviceSupported", intentService.isDeviceActivityAvailable(deviceType));
            result.put("novaICareVersion", intentService.getNovaICareVersion());

            call.resolve(result);

        } catch (Exception e) {
            Log.e(TAG, "❌ Error checking device availability", e);
            call.reject("Failed to check device availability: " + e.getMessage());
        }
    }

    @PluginMethod
    public void getDeviceStatus(PluginCall call) {
        try {
            String deviceType = call.getString("deviceType", "HEMOGLOBIN");

            JSObject result = new JSObject();
            result.put("connected", false);
            result.put("deviceType", deviceType);
            result.put("platform", "android");
            result.put("communicationMethod", "intent");
            result.put("timestamp", System.currentTimeMillis());

            call.resolve(result);

        } catch (Exception e) {
            Log.e(TAG, "❌ Error getting device status", e);
            call.reject("Failed to get device status: " + e.getMessage());
        }
    }

    @PluginMethod
    public void disconnectDevice(PluginCall call) {
        try {
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "Android intents don't require explicit disconnection");
            call.resolve(result);
        } catch (Exception e) {
            Log.e(TAG, "❌ Error disconnecting device", e);
            call.reject("Failed to disconnect device: " + e.getMessage());
        }
    }

    @PluginMethod
    public void getSupportedDevices(PluginCall call) {
        try {
            String[] supportedDevices = intentService.getSupportedDeviceTypes();

            JSObject result = new JSObject();
            result.put("devices", supportedDevices);
            result.put("platform", "android");
            result.put("novaICareInstalled", intentService.isNovaICareInstalled());
            result.put("novaICareVersion", intentService.getNovaICareVersion());

            call.resolve(result);
        } catch (Exception e) {
            Log.e(TAG, "❌ Error getting supported devices", e);
            call.reject("Failed to get supported devices: " + e.getMessage());
        }
    }

    @ActivityCallback
    private void handleDeviceResult(PluginCall call, int resultCode, Intent data) {
        try {
            Log.d(TAG, "📥 Handling device result, resultCode: " + resultCode);
            
            // Enhanced logging for debugging
            if (data != null && data.getExtras() != null) {
                Log.d(TAG, "📊 Intent extras received: " + data.getExtras().keySet().toString());
            }

            if (resultCode == android.app.Activity.RESULT_OK && data != null) {
                String deviceType = call.getString("deviceType", "HEMOGLOBIN");
                String sessionId = call.getString("sessionId", "");

                Log.d(TAG, "✅ Processing successful result for device: " + deviceType);

                NovaICareIntentService.DeviceResult deviceResult =
                        intentService.parseIntentResult(data, deviceType);

                // Validate result before processing
                if (deviceResult == null) {
                    Log.e(TAG, "❌ Parsed result is null");
                    call.reject("Failed to parse device result");
                    return;
                }

                JSObject jsResult = convertDeviceResultToJS(deviceResult);

                // Notify listeners in a try-catch to prevent crashes
                try {
                    notifyDeviceResultListeners(deviceResult);
                } catch (Exception listenerError) {
                    Log.e(TAG, "❌ Error notifying listeners (non-fatal)", listenerError);
                    // Continue execution - listener errors shouldn't fail the main operation
                }

                call.resolve(jsResult);
                Log.d(TAG, "✅ Device result handled successfully for: " + deviceType);

            } else if (resultCode == android.app.Activity.RESULT_CANCELED) {
                Log.w(TAG, "⚠️ Device operation was cancelled by user");
                
                // Create a cancelled result for consistency
                JSObject cancelledResult = new JSObject();
                cancelledResult.put("success", false);
                cancelledResult.put("cancelled", true);
                cancelledResult.put("error", "Operation cancelled by user");
                cancelledResult.put("resultCode", resultCode);
                cancelledResult.put("timestamp", System.currentTimeMillis());
                
                call.resolve(cancelledResult); // Use resolve instead of reject for user cancellation

            } else {
                Log.w(TAG, "❌ Device operation failed with result code: " + resultCode);
                
                // Create detailed error result
                JSObject errorResult = new JSObject();
                errorResult.put("success", false);
                errorResult.put("error", "Device operation failed");
                errorResult.put("resultCode", resultCode);
                errorResult.put("timestamp", System.currentTimeMillis());
                
                if (data != null) {
                    errorResult.put("hasData", true);
                    // Try to extract any error information from the intent
                    String errorMsg = data.getStringExtra("error");
                    if (errorMsg != null) {
                        errorResult.put("deviceError", errorMsg);
                    }
                } else {
                    errorResult.put("hasData", false);
                }
                
                call.resolve(errorResult); // Use resolve to provide structured error info
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ Critical error handling device result", e);
            
            // Create comprehensive error response
            JSObject errorResult = new JSObject();
            errorResult.put("success", false);
            errorResult.put("error", "Critical error: " + e.getMessage());
            errorResult.put("errorType", e.getClass().getSimpleName());
            errorResult.put("resultCode", resultCode);
            errorResult.put("timestamp", System.currentTimeMillis());
            
            call.resolve(errorResult); // Use resolve to ensure client gets error info
        }
    }

    private JSObject convertDeviceResultToJS(NovaICareIntentService.DeviceResult result) {
        JSObject jsResult = new JSObject();
        jsResult.put("success", result.success);
        jsResult.put("deviceType", result.deviceType);
        jsResult.put("timestamp", result.timestamp);
        jsResult.put("platform", "android");
        jsResult.put("communicationMethod", "intent");

        if (result.sessionId != null) {
            jsResult.put("sessionId", result.sessionId);
        }

        if (result.success && result.data != null) {
            JSObject dataObject = new JSObject();
            for (Map.Entry<String, Object> entry : result.data.entrySet()) {
                dataObject.put(entry.getKey(), entry.getValue());
            }
            jsResult.put("data", dataObject);
        }

        if (!result.success && result.error != null) {
            jsResult.put("error", result.error);
        }

        return jsResult;
    }

    private void notifyDeviceResultListeners(NovaICareIntentService.DeviceResult result) {
        try {
            JSObject eventData = convertDeviceResultToJS(result);
            notifyListeners("DeviceResult", eventData);

            switch (result.deviceType) {
                case "PULSE_OXIMETER":
                    notifyListeners("PulseOximeterResult", eventData);
                    break;
                case "STETHOSCOPE":
                    notifyListeners("StethoscopeResult", eventData);
                    break;
                case "THERMOMETER":
                    notifyListeners("ThermometerResult", eventData);
                    break;
                case "BLOOD_PRESSURE":
                    notifyListeners("BloodPressureResult", eventData);
                    break;
                case "ECG":
                    notifyListeners("ECGResult", eventData);
                    break;
                case "HEMOGLOBIN":
                    notifyListeners("HemoglobinResult", eventData);
                    break;
            }

            Log.d(TAG, "📡 Device result listeners notified: " + result.deviceType);
        } catch (Exception e) {
            Log.e(TAG, "❌ Error notifying device result listeners", e);
        }
    }

    public void handleIncomingIntent(Intent intent) {
        try {
            if (intent != null && intent.hasExtra("device_type")) {
                String deviceType = intent.getStringExtra("device_type");
                Log.d(TAG, "📥 Handling incoming intent for device: " + deviceType);

                NovaICareIntentService.DeviceResult result =
                        intentService.parseIntentResult(intent, deviceType);

                notifyDeviceResultListeners(result);
                Log.d(TAG, "✅ Incoming intent handled successfully");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ Error handling incoming intent", e);
        }
    }

    public void sendDeviceResult(String deviceType, Map<String, String> data) {
        try {
            Log.d(TAG, "📤 Sending device result: " + deviceType);

            JSObject eventData = new JSObject();
            eventData.put("success", true);
            eventData.put("deviceType", deviceType);
            eventData.put("timestamp", System.currentTimeMillis());
            eventData.put("platform", "android");
            eventData.put("communicationMethod", "intent");

            JSObject dataObject = new JSObject();
            for (Map.Entry<String, String> entry : data.entrySet()) {
                dataObject.put(entry.getKey(), entry.getValue());
            }
            eventData.put("data", dataObject);

            notifyListeners("DeviceResult", eventData);

            switch (deviceType) {
                case "PULSE_OXIMETER":
                    notifyListeners("PulseOximeterResult", eventData);
                    break;
                case "STETHOSCOPE":
                    notifyListeners("StethoscopeResult", eventData);
                    break;
                case "THERMOMETER":
                    notifyListeners("ThermometerResult", eventData);
                    break;
                case "BLOOD_PRESSURE":
                    notifyListeners("BloodPressureResult", eventData);
                    break;
                case "ECG":
                    notifyListeners("ECGResult", eventData);
                    break;

                case "OPTICAL_READER":
                    notifyListeners("opticalreaderlis", eventData);
                    break;
                    
                case "HEMOGLOBIN":
                    notifyListeners("HemoglobinmeterResult", eventData);
                    break;
            }

            Log.d(TAG, "✅ Device result sent successfully");

        } catch (Exception e) {
            Log.e(TAG, "❌ Error sending device result", e);
        }
    }

    private String generateCallId() {
        return "call_" + System.currentTimeMillis() + "_" + Integer.toHexString((int) (Math.random() * 0x10000));
    }

    private void cleanupPendingCalls() {
        pendingCalls.clear();
    }

    @Override
    protected void handleOnDestroy() {
        cleanupPendingCalls();
        super.handleOnDestroy();
    }
}
