.ecg-container {
  background: #f5f7fa;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 0 8px rgba(0,0,0,0.1);
  max-width: 500px;
  margin: 20px auto;
  font-family: 'Segoe UI', sans-serif;
}

.ecg-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.ecg-header h2 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: transparent;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}

.close-btn:hover {
  color: #d00;
}

.launch-btn {
  background-color: #007bff;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
}

.launch-btn:hover {
  background-color: #0056b3;
}

.ecg-result {
  margin-top: 20px;
  background: #fff;
  padding: 16px;
  border-radius: 10px;
  border: 1px solid #ddd;
}

.ecg-result h4 {
  margin-top: 0;
  color: #444;
}

.ecg-result p {
  margin: 6px 0;
  color: #555;
}
