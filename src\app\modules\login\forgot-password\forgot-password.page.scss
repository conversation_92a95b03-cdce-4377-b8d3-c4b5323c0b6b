.login-container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #eef2f5;
    background-size: cover;
}

.login-box {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 30px 40px;
    width: 320px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.veli_title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 25px;
    color: #333;
}

.input-group {
    position: relative;
    margin-bottom: 20px;
    input {
        width: 100%;
        padding: 10px 10px 10px 40px;
        border: 1px solid #ccc;
        border-radius: 8px;
        font-size: 14px;
    }
    .icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        color: #888;
    }
    .user-icon::before {
        content: '\f007';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
    }
    .lock-icon::before {
        content: '\f023';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
    }
}

.form-mandatory {
    font-size: 12px;
    color: red;
    margin-bottom: 10px;
    text-align: left;
}

.login-btn {
    width: 100%;
    padding: 10px;
    background-color: #4d8cff;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    font-size: 15px;
    cursor: pointer;
    &:hover {
        background-color: #4079e3;
    }
}

.small-alert-popup {
    padding: 10px !important;
    min-height: unset !important;
    max-height: 200px !important;
}

.small-alert-popup {
    padding: 8px !important;
    font-size: 14px;
    max-height: 180px;
}

.error {
    color: red;
    font-size: 12px;
    margin-top: 4px;
}

.minimal-swal-popup {
    font-size: 10px !important; // text size
    border-radius: 6px !important;
    padding: 1em !important;
}

.minimal-swal-button {
    background-color: #007bff !important;
    color: white !important;
    font-size: 10px !important; // button font size
    padding: 4px 10px !important;
    border-radius: 4px !important;
    height: 28px !important;
    min-width: 50px !important;
}

.minimal-swal-actions {
    justify-content: flex-end !important; // align button to right
}