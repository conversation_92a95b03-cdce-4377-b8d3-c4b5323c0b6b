<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- ✅ Allow all HTTPS traffic for production server -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">s3test2.remedi.co.in</domain>
        <domain includeSubdomains="true">remedi.co.in</domain>
    </domain-config>
    
    <!-- ✅ Allow localhost for development (if needed) -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
    
    <!-- ✅ Base configuration -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
